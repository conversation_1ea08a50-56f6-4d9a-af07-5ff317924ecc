{"version": 3, "sources": ["../../cssesc/cssesc.js"], "sourcesContent": ["/*! https://mths.be/cssesc v3.0.0 by @mathias */\n'use strict';\n\nvar object = {};\nvar hasOwnProperty = object.hasOwnProperty;\nvar merge = function merge(options, defaults) {\n\tif (!options) {\n\t\treturn defaults;\n\t}\n\tvar result = {};\n\tfor (var key in defaults) {\n\t\t// `if (defaults.hasOwnProperty(key) { … }` is not needed here, since\n\t\t// only recognized option names are used.\n\t\tresult[key] = hasOwnProperty.call(options, key) ? options[key] : defaults[key];\n\t}\n\treturn result;\n};\n\nvar regexAnySingleEscape = /[ -,\\.\\/:-@\\[-\\^`\\{-~]/;\nvar regexSingleEscape = /[ -,\\.\\/:-@\\[\\]\\^`\\{-~]/;\nvar regexAlwaysEscape = /['\"\\\\]/;\nvar regexExcessiveSpaces = /(^|\\\\+)?(\\\\[A-F0-9]{1,6})\\x20(?![a-fA-F0-9\\x20])/g;\n\n// https://mathiasbynens.be/notes/css-escapes#css\nvar cssesc = function cssesc(string, options) {\n\toptions = merge(options, cssesc.options);\n\tif (options.quotes != 'single' && options.quotes != 'double') {\n\t\toptions.quotes = 'single';\n\t}\n\tvar quote = options.quotes == 'double' ? '\"' : '\\'';\n\tvar isIdentifier = options.isIdentifier;\n\n\tvar firstChar = string.charAt(0);\n\tvar output = '';\n\tvar counter = 0;\n\tvar length = string.length;\n\twhile (counter < length) {\n\t\tvar character = string.charAt(counter++);\n\t\tvar codePoint = character.charCodeAt();\n\t\tvar value = void 0;\n\t\t// If it’s not a printable ASCII character…\n\t\tif (codePoint < 0x20 || codePoint > 0x7E) {\n\t\t\tif (codePoint >= 0xD800 && codePoint <= 0xDBFF && counter < length) {\n\t\t\t\t// It’s a high surrogate, and there is a next character.\n\t\t\t\tvar extra = string.charCodeAt(counter++);\n\t\t\t\tif ((extra & 0xFC00) == 0xDC00) {\n\t\t\t\t\t// next character is low surrogate\n\t\t\t\t\tcodePoint = ((codePoint & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000;\n\t\t\t\t} else {\n\t\t\t\t\t// It’s an unmatched surrogate; only append this code unit, in case\n\t\t\t\t\t// the next code unit is the high surrogate of a surrogate pair.\n\t\t\t\t\tcounter--;\n\t\t\t\t}\n\t\t\t}\n\t\t\tvalue = '\\\\' + codePoint.toString(16).toUpperCase() + ' ';\n\t\t} else {\n\t\t\tif (options.escapeEverything) {\n\t\t\t\tif (regexAnySingleEscape.test(character)) {\n\t\t\t\t\tvalue = '\\\\' + character;\n\t\t\t\t} else {\n\t\t\t\t\tvalue = '\\\\' + codePoint.toString(16).toUpperCase() + ' ';\n\t\t\t\t}\n\t\t\t} else if (/[\\t\\n\\f\\r\\x0B]/.test(character)) {\n\t\t\t\tvalue = '\\\\' + codePoint.toString(16).toUpperCase() + ' ';\n\t\t\t} else if (character == '\\\\' || !isIdentifier && (character == '\"' && quote == character || character == '\\'' && quote == character) || isIdentifier && regexSingleEscape.test(character)) {\n\t\t\t\tvalue = '\\\\' + character;\n\t\t\t} else {\n\t\t\t\tvalue = character;\n\t\t\t}\n\t\t}\n\t\toutput += value;\n\t}\n\n\tif (isIdentifier) {\n\t\tif (/^-[-\\d]/.test(output)) {\n\t\t\toutput = '\\\\-' + output.slice(1);\n\t\t} else if (/\\d/.test(firstChar)) {\n\t\t\toutput = '\\\\3' + firstChar + ' ' + output.slice(1);\n\t\t}\n\t}\n\n\t// Remove spaces after `\\HEX` escapes that are not followed by a hex digit,\n\t// since they’re redundant. Note that this is only possible if the escape\n\t// sequence isn’t preceded by an odd number of backslashes.\n\toutput = output.replace(regexExcessiveSpaces, function ($0, $1, $2) {\n\t\tif ($1 && $1.length % 2) {\n\t\t\t// It’s not safe to remove the space, so don’t.\n\t\t\treturn $0;\n\t\t}\n\t\t// Strip the space.\n\t\treturn ($1 || '') + $2;\n\t});\n\n\tif (!isIdentifier && options.wrap) {\n\t\treturn quote + output + quote;\n\t}\n\treturn output;\n};\n\n// Expose default options (so they can be overridden globally).\ncssesc.options = {\n\t'escapeEverything': false,\n\t'isIdentifier': false,\n\t'quotes': 'single',\n\t'wrap': false\n};\n\ncssesc.version = '3.0.0';\n\nmodule.exports = cssesc;\n"], "mappings": ";;;;;AAAA;AAAA;AAGA,QAAI,SAAS,CAAC;AACd,QAAI,iBAAiB,OAAO;AAC5B,QAAI,QAAQ,SAASA,OAAM,SAAS,UAAU;AAC7C,UAAI,CAAC,SAAS;AACb,eAAO;AAAA,MACR;AACA,UAAI,SAAS,CAAC;AACd,eAAS,OAAO,UAAU;AAGzB,eAAO,GAAG,IAAI,eAAe,KAAK,SAAS,GAAG,IAAI,QAAQ,GAAG,IAAI,SAAS,GAAG;AAAA,MAC9E;AACA,aAAO;AAAA,IACR;AAEA,QAAI,uBAAuB;AAC3B,QAAI,oBAAoB;AAExB,QAAI,uBAAuB;AAG3B,QAAI,SAAS,SAASC,QAAO,QAAQ,SAAS;AAC7C,gBAAU,MAAM,SAASA,QAAO,OAAO;AACvC,UAAI,QAAQ,UAAU,YAAY,QAAQ,UAAU,UAAU;AAC7D,gBAAQ,SAAS;AAAA,MAClB;AACA,UAAI,QAAQ,QAAQ,UAAU,WAAW,MAAM;AAC/C,UAAI,eAAe,QAAQ;AAE3B,UAAI,YAAY,OAAO,OAAO,CAAC;AAC/B,UAAI,SAAS;AACb,UAAI,UAAU;AACd,UAAI,SAAS,OAAO;AACpB,aAAO,UAAU,QAAQ;AACxB,YAAI,YAAY,OAAO,OAAO,SAAS;AACvC,YAAI,YAAY,UAAU,WAAW;AACrC,YAAI,QAAQ;AAEZ,YAAI,YAAY,MAAQ,YAAY,KAAM;AACzC,cAAI,aAAa,SAAU,aAAa,SAAU,UAAU,QAAQ;AAEnE,gBAAI,QAAQ,OAAO,WAAW,SAAS;AACvC,iBAAK,QAAQ,UAAW,OAAQ;AAE/B,4BAAc,YAAY,SAAU,OAAO,QAAQ,QAAS;AAAA,YAC7D,OAAO;AAGN;AAAA,YACD;AAAA,UACD;AACA,kBAAQ,OAAO,UAAU,SAAS,EAAE,EAAE,YAAY,IAAI;AAAA,QACvD,OAAO;AACN,cAAI,QAAQ,kBAAkB;AAC7B,gBAAI,qBAAqB,KAAK,SAAS,GAAG;AACzC,sBAAQ,OAAO;AAAA,YAChB,OAAO;AACN,sBAAQ,OAAO,UAAU,SAAS,EAAE,EAAE,YAAY,IAAI;AAAA,YACvD;AAAA,UACD,WAAW,iBAAiB,KAAK,SAAS,GAAG;AAC5C,oBAAQ,OAAO,UAAU,SAAS,EAAE,EAAE,YAAY,IAAI;AAAA,UACvD,WAAW,aAAa,QAAQ,CAAC,iBAAiB,aAAa,OAAO,SAAS,aAAa,aAAa,OAAQ,SAAS,cAAc,gBAAgB,kBAAkB,KAAK,SAAS,GAAG;AAC1L,oBAAQ,OAAO;AAAA,UAChB,OAAO;AACN,oBAAQ;AAAA,UACT;AAAA,QACD;AACA,kBAAU;AAAA,MACX;AAEA,UAAI,cAAc;AACjB,YAAI,UAAU,KAAK,MAAM,GAAG;AAC3B,mBAAS,QAAQ,OAAO,MAAM,CAAC;AAAA,QAChC,WAAW,KAAK,KAAK,SAAS,GAAG;AAChC,mBAAS,QAAQ,YAAY,MAAM,OAAO,MAAM,CAAC;AAAA,QAClD;AAAA,MACD;AAKA,eAAS,OAAO,QAAQ,sBAAsB,SAAU,IAAI,IAAI,IAAI;AACnE,YAAI,MAAM,GAAG,SAAS,GAAG;AAExB,iBAAO;AAAA,QACR;AAEA,gBAAQ,MAAM,MAAM;AAAA,MACrB,CAAC;AAED,UAAI,CAAC,gBAAgB,QAAQ,MAAM;AAClC,eAAO,QAAQ,SAAS;AAAA,MACzB;AACA,aAAO;AAAA,IACR;AAGA,WAAO,UAAU;AAAA,MAChB,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,QAAQ;AAAA,IACT;AAEA,WAAO,UAAU;AAEjB,WAAO,UAAU;AAAA;AAAA;", "names": ["merge", "cssesc"]}