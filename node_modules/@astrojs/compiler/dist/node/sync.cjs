"use strict";var I=Object.create;var y=Object.defineProperty;var P=Object.getOwnPropertyDescriptor;var A=Object.getOwnPropertyNames;var E=Object.getPrototypeOf,C=Object.prototype.hasOwnProperty;var N=(i,r)=>{for(var s in r)y(i,s,{get:r[s],enumerable:!0})},T=(i,r,s,n)=>{if(r&&typeof r=="object"||typeof r=="function")for(let c of A(r))!C.call(i,c)&&c!==s&&y(i,c,{get:()=>r[c],enumerable:!(n=P(r,c))||n.enumerable});return i};var x=(i,r,s)=>(s=i!=null?I(E(i)):{},T(r||!i||!i.__esModule?y(s,"default",{value:i,enumerable:!0}):s,i)),O=i=>T(y({},"__esModule",{value:!0}),i);var M={};N(M,{convertToTSX:()=>L,parse:()=>F,startRunningService:()=>R,transform:()=>V});module.exports=O(M);var D=()=>typeof document>"u"?new URL("file:"+__filename).href:document.currentScript&&document.currentScript.src||new URL("main.js",document.baseURI).href,b=D();var S=require("fs"),j=require("url");var _=x(require("crypto"),1),w=x(require("fs"),1),v=require("util");globalThis.fs||Object.defineProperty(globalThis,"fs",{value:w.default});globalThis.process||Object.defineProperties(globalThis,"process",{value:process});globalThis.crypto||Object.defineProperty(globalThis,"crypto",{value:_.default.webcrypto?_.default.webcrypto:{getRandomValues(i){return _.default.randomFillSync(i)}}});globalThis.performance||Object.defineProperty(globalThis,"performance",{value:{now(){let[i,r]=process.hrtime();return i*1e3+r/1e6}}});var U=new v.TextEncoder("utf-8"),k=new v.TextDecoder("utf-8");var d=class{constructor(){this.argv=["js"],this.env={},this.exit=e=>{e!==0&&console.warn("exit code:",e)},this._exitPromise=new Promise(e=>{this._resolveExitPromise=e}),this._pendingEvent=null,this._scheduledTimeouts=new Map,this._nextCallbackTimeoutID=1;let r=(e,t)=>{this.mem.setUint32(e+0,t,!0),this.mem.setUint32(e+4,Math.floor(t/4294967296),!0)},s=e=>{let t=this.mem.getUint32(e+0,!0),o=this.mem.getInt32(e+4,!0);return t+o*4294967296},n=e=>{let t=this.mem.getFloat64(e,!0);if(t===0)return;if(!isNaN(t))return t;let o=this.mem.getUint32(e,!0);return this._values[o]},c=(e,t)=>{if(typeof t=="number"&&t!==0){if(isNaN(t)){this.mem.setUint32(e+4,2146959360,!0),this.mem.setUint32(e,0,!0);return}this.mem.setFloat64(e,t,!0);return}if(t===void 0){this.mem.setFloat64(e,0,!0);return}let a=this._ids.get(t);a===void 0&&(a=this._idPool.pop(),a===void 0&&(a=this._values.length),this._values[a]=t,this._goRefCounts[a]=0,this._ids.set(t,a)),this._goRefCounts[a]++;let m=0;switch(typeof t){case"object":t!==null&&(m=1);break;case"string":m=2;break;case"symbol":m=3;break;case"function":m=4;break}this.mem.setUint32(e+4,2146959360|m,!0),this.mem.setUint32(e,a,!0)},l=e=>{let t=s(e+0),o=s(e+8);return new Uint8Array(this._inst.exports.mem.buffer,t,o)},g=e=>{let t=s(e+0),o=s(e+8),a=new Array(o);for(let m=0;m<o;m++)a[m]=n(t+m*8);return a},h=e=>{let t=s(e+0),o=s(e+8);return k.decode(new DataView(this._inst.exports.mem.buffer,t,o))},u=Date.now()-performance.now();this.importObject={gojs:{"runtime.wasmExit":e=>{e>>>=0;let t=this.mem.getInt32(e+8,!0);this.exited=!0,delete this._inst,delete this._values,delete this._goRefCounts,delete this._ids,delete this._idPool,this.exit(t)},"runtime.wasmWrite":e=>{e>>>=0;let t=s(e+8),o=s(e+16),a=this.mem.getInt32(e+24,!0);w.default.writeSync(t,new Uint8Array(this._inst.exports.mem.buffer,o,a))},"runtime.resetMemoryDataView":e=>{e>>>=0,this.mem=new DataView(this._inst.exports.mem.buffer)},"runtime.nanotime1":e=>{e>>>=0,r(e+8,(u+performance.now())*1e6)},"runtime.walltime":e=>{e>>>=0;let t=new Date().getTime();r(e+8,t/1e3),this.mem.setInt32(e+16,t%1e3*1e6,!0)},"runtime.scheduleTimeoutEvent":e=>{e>>>=0;let t=this._nextCallbackTimeoutID;this._nextCallbackTimeoutID++,this._scheduledTimeouts.set(t,setTimeout(()=>{for(this._resume();this._scheduledTimeouts.has(t);)console.warn("scheduleTimeoutEvent: missed timeout event"),this._resume()},s(e+8)+1)),this.mem.setInt32(e+16,t,!0)},"runtime.clearTimeoutEvent":e=>{e>>>=0;let t=this.mem.getInt32(e+8,!0);clearTimeout(this._scheduledTimeouts.get(t)),this._scheduledTimeouts.delete(t)},"runtime.getRandomData":e=>{e>>>=0,globalThis.crypto.getRandomValues(l(e+8))},"syscall/js.finalizeRef":e=>{e>>>=0;let t=this.mem.getUint32(e+8,!0);if(this._goRefCounts[t]--,this._goRefCounts[t]===0){let o=this._values[t];this._values[t]=null,this._ids.delete(o),this._idPool.push(t)}},"syscall/js.stringVal":e=>{e>>>=0,c(e+24,h(e+8))},"syscall/js.valueGet":e=>{e>>>=0;let t=Reflect.get(n(e+8),h(e+16));e=this._inst.exports.getsp()>>>0,c(e+32,t)},"syscall/js.valueSet":e=>{e>>>=0,Reflect.set(n(e+8),h(e+16),n(e+32))},"syscall/js.valueDelete":e=>{e>>>=0,Reflect.deleteProperty(n(e+8),h(e+16))},"syscall/js.valueIndex":e=>{e>>>=0,c(e+24,Reflect.get(n(e+8),s(e+16)))},"syscall/js.valueSetIndex":e=>{e>>>=0,Reflect.set(n(e+8),s(e+16),n(e+24))},"syscall/js.valueCall":e=>{e>>>=0;try{let t=n(e+8),o=Reflect.get(t,h(e+16)),a=g(e+32),m=Reflect.apply(o,t,a);e=this._inst.exports.getsp()>>>0,c(e+56,m),this.mem.setUint8(e+64,1)}catch(t){e=this._inst.exports.getsp()>>>0,c(e+56,t),this.mem.setUint8(e+64,0)}},"syscall/js.valueInvoke":e=>{e>>>=0;try{let t=n(e+8),o=g(e+16),a=Reflect.apply(t,void 0,o);e=this._inst.exports.getsp()>>>0,c(e+40,a),this.mem.setUint8(e+48,1)}catch(t){e=this._inst.exports.getsp()>>>0,c(e+40,t),this.mem.setUint8(e+48,0)}},"syscall/js.valueNew":e=>{e>>>=0;try{let t=n(e+8),o=g(e+16),a=Reflect.construct(t,o);e=this._inst.exports.getsp()>>>0,c(e+40,a),this.mem.setUint8(e+48,1)}catch(t){e=this._inst.exports.getsp()>>>0,c(e+40,t),this.mem.setUint8(e+48,0)}},"syscall/js.valueLength":e=>{e>>>=0,r(e+16,Number.parseInt(n(e+8).length))},"syscall/js.valuePrepareString":e=>{e>>>=0;let t=U.encode(String(n(e+8)));c(e+16,t),r(e+24,t.length)},"syscall/js.valueLoadString":e=>{e>>>=0;let t=n(e+8);l(e+16).set(t)},"syscall/js.valueInstanceOf":e=>{e>>>=0,this.mem.setUint8(e+24,n(e+8)instanceof n(e+16)?1:0)},"syscall/js.copyBytesToGo":e=>{e>>>=0;let t=l(e+8),o=n(e+32);if(!(o instanceof Uint8Array||o instanceof Uint8ClampedArray)){this.mem.setUint8(e+48,0);return}let a=o.subarray(0,t.length);t.set(a),r(e+40,a.length),this.mem.setUint8(e+48,1)},"syscall/js.copyBytesToJS":e=>{e>>>=0;let t=n(e+8),o=l(e+16);if(!(t instanceof Uint8Array||t instanceof Uint8ClampedArray)){this.mem.setUint8(e+48,0);return}let a=o.subarray(0,t.length);t.set(a),r(e+40,a.length),this.mem.setUint8(e+48,1)},debug:e=>{console.log(e)}}}}async run(r){if(!(r instanceof WebAssembly.Instance))throw new Error("Go.run: WebAssembly.Instance expected");this._inst=r,this.mem=new DataView(this._inst.exports.mem.buffer),this._values=[Number.NaN,0,null,!0,!1,globalThis,this],this._goRefCounts=new Array(this._values.length).fill(Number.POSITIVE_INFINITY),this._ids=new Map([[0,1],[null,2],[!0,3],[!1,4],[globalThis,5],[this,6]]),this._idPool=[],this.exited=!1;let s=4096,n=u=>{let e=s,t=U.encode(`${u}\0`);return new Uint8Array(this.mem.buffer,s,t.length).set(t),s+=t.length,s%8!==0&&(s+=8-s%8),e},c=this.argv.length,l=[];this.argv.forEach(u=>{l.push(n(u))}),l.push(0),Object.keys(this.env).sort().forEach(u=>{l.push(n(`${u}=${this.env[u]}`))}),l.push(0);let h=s;l.forEach(u=>{this.mem.setUint32(s,u,!0),this.mem.setUint32(s+4,0,!0),s+=8}),this._inst.exports.run(c,h),this.exited&&this._resolveExitPromise(),await this._exitPromise}_resume(){if(this.exited)throw new Error("Go program has already exited");this._inst.exports.resume(),this.exited&&this._resolveExitPromise()}_makeFuncWrapper(r){let s=this;return function(){let n={id:r,this:this,args:arguments};return s._pendingEvent=n,s._resume(),n.result}}};function p(){return f||(f=R()),f}var f,V=(i,r)=>p().transform(i,r),F=(i,r)=>p().parse(i,r),L=(i,r)=>p().convertToTSX(i,r);function R(){let i=new d,r=W((0,j.fileURLToPath)(new URL("../astro.wasm",b)),i.importObject);i.run(r);let s=globalThis["@astrojs/compiler"];return{transform:(n,c)=>{try{return s.transform(n,c||{})}catch(l){throw f=void 0,l}},parse:(n,c)=>{try{let l=s.parse(n,c||{});return{...l,ast:JSON.parse(l.ast)}}catch(l){throw f=void 0,l}},convertToTSX:(n,c)=>{try{let l=s.convertToTSX(n,c||{});return{...l,map:JSON.parse(l.map)}}catch(l){throw f=void 0,l}}}}function W(i,r){let s=(0,S.readFileSync)(i);return new WebAssembly.Instance(new WebAssembly.Module(s),r)}0&&(module.exports={convertToTSX,parse,startRunningService,transform});
