---
import { getLangFromUrl, useTranslations } from "../utils/i18n";

const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);

const currentYear = new Date().getFullYear();
---

<footer class="bg-dark-100 border-t border-white/10">
  <div class="container-width section-padding">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
      <!-- Brand Section -->
      <div class="md:col-span-2">
        <div class="flex items-center space-x-3 mb-4">
          <img
            src="/impactadvocacydfwlogo.png"
            alt="Impact Advocacy Logo"
            class="h-12 w-auto"
          />
          <span class="text-2xl font-bold gradient-text">
            Impact Advocacy
          </span>
        </div>
        <p class="text-white/70 mb-6 max-w-md">
          {
            currentLang === "es"
              ? "Empoderando a las comunidades latinas a través de la educación financiera, educación y defensa estratégica en el área de Dallas-Fort Worth."
              : "Empowering Latino communities through financial literacy, education, and strategic advocacy in the Dallas-Fort Worth area."
          }
        </p>

        <!-- Social Media Links -->
        <div class="flex space-x-4">
          <a
            href="https://facebook.com/impactadvocacy"
            target="_blank"
            rel="noopener noreferrer"
            class="p-2 glass-card rounded-lg hover:bg-white/10 transition-all duration-300"
            aria-label="Facebook"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path
                d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
              ></path>
            </svg>
          </a>
          <a
            href="https://instagram.com/impactadvocacy"
            target="_blank"
            rel="noopener noreferrer"
            class="p-2 glass-card rounded-lg hover:bg-white/10 transition-all duration-300"
            aria-label="Instagram"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path
                d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.802 8.196 6.953 7.706 8.25 7.706s2.448.49 3.323 1.297c.876.876 1.366 2.027 1.366 3.324s-.49 2.448-1.366 3.323c-.875.807-2.026 1.297-3.323 1.297z"
              ></path>
            </svg>
          </a>
          <a
            href="https://tiktok.com/@impactadvocacy"
            target="_blank"
            rel="noopener noreferrer"
            class="p-2 glass-card rounded-lg hover:bg-white/10 transition-all duration-300"
            aria-label="TikTok"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path
                d="M19.59 6.69a4.83 4.83 0 01-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 01-5.2 1.74 2.89 2.89 0 012.31-4.64 2.93 2.93 0 01.88.13V9.4a6.84 6.84 0 00-.88-.05A6.33 6.33 0 005 20.1a6.34 6.34 0 0010.86-4.43v-7a8.16 8.16 0 004.77 1.52v-3.4a4.85 4.85 0 01-1-.1z"
              ></path>
            </svg>
          </a>
          <a
            href="https://twitter.com/impactadvocacy"
            target="_blank"
            rel="noopener noreferrer"
            class="p-2 glass-card rounded-lg hover:bg-white/10 transition-all duration-300"
            aria-label="Twitter/X"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path
                d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"
              ></path>
            </svg>
          </a>
        </div>
      </div>

      <!-- Quick Links -->
      <div>
        <h3 class="text-lg font-semibold text-white mb-4">
          {currentLang === "es" ? "Enlaces Rápidos" : "Quick Links"}
        </h3>
        <ul class="space-y-2">
          <li>
            <a
              href={currentLang === "es" ? "/es/" : "/"}
              class="text-white/70 hover:text-white transition-colors duration-300"
            >
              {t("nav.home")}
            </a>
          </li>
          <li>
            <a
              href={currentLang === "es" ? "/es/about" : "/about"}
              class="text-white/70 hover:text-white transition-colors duration-300"
            >
              {t("nav.about")}
            </a>
          </li>
          <li>
            <a
              href={currentLang === "es" ? "/es/services" : "/services"}
              class="text-white/70 hover:text-white transition-colors duration-300"
            >
              {t("nav.services")}
            </a>
          </li>
          <li>
            <a
              href={currentLang === "es" ? "/es/events" : "/events"}
              class="text-white/70 hover:text-white transition-colors duration-300"
            >
              {t("nav.events")}
            </a>
          </li>
          <li>
            <a
              href={currentLang === "es" ? "/es/contact" : "/contact"}
              class="text-white/70 hover:text-white transition-colors duration-300"
            >
              {t("nav.contact")}
            </a>
          </li>
        </ul>
      </div>

      <!-- Contact Info -->
      <div>
        <h3 class="text-lg font-semibold text-white mb-4">
          {currentLang === "es" ? "Contacto" : "Contact"}
        </h3>
        <ul class="space-y-2 text-white/70">
          <li class="flex items-center">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"
              ></path>
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"
              ></path>
            </svg>
            <a
              href="mailto:<EMAIL>"
              class="hover:text-white transition-colors"
            >
              <EMAIL>
            </a>
          </li>
          <li class="flex items-center">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
              ></path>
            </svg>
            <a
              href="tel:+19498270748"
              class="hover:text-white transition-colors"
            >
              +****************
            </a>
          </li>
          <li class="flex items-start">
            <svg
              class="w-4 h-4 mr-2 mt-1"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                clip-rule="evenodd"></path>
            </svg>
            <span>
              {
                currentLang === "es"
                  ? "Sirviendo el área de Dallas-Fort Worth"
                  : "Serving the Dallas-Fort Worth Area"
              }
            </span>
          </li>
        </ul>
      </div>
    </div>

    <!-- Bottom Section -->
    <div class="border-t border-white/10 mt-8 pt-8">
      <div
        class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0"
      >
        <div class="text-white/70 text-sm">
          <p>© {currentYear} {t("footer.copyright")}</p>
          <p class="mt-1">{t("footer.disclaimer")}</p>
        </div>

        <div class="flex items-center space-x-4 text-sm text-white/70">
          <span>
            {currentLang === "es" ? "Desarrollado por" : "Powered by"}
            <a
              href="https://elgatoai.com"
              target="_blank"
              rel="noopener noreferrer"
              class="text-primary-400 hover:text-primary-300"
            >
              El Gato AI
            </a>
          </span>
        </div>
      </div>
    </div>
  </div>
</footer>
