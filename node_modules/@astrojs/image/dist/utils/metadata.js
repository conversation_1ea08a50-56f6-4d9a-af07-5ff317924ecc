import sizeOf from "image-size";
import fs from "node:fs/promises";
import { fileURLToPath } from "node:url";
async function metadata(src, data) {
  const file = data || await fs.readFile(src);
  const { width, height, type, orientation } = sizeOf(file);
  const isPortrait = (orientation || 0) >= 5;
  if (!width || !height || !type) {
    return void 0;
  }
  return {
    // We shouldn't call fileURLToPath function if it starts with /@astroimage/ because it will throw Invalid URL error
    src: typeof src === "string" && /^[\/\\]?@astroimage/.test(src) ? src : fileURLToPath(src),
    width: isPortrait ? height : width,
    height: isPortrait ? width : height,
    format: type,
    orientation
  };
}
export {
  metadata
};
