---
import { getLangFromUrl, useTranslations } from "../utils/i18n";

export interface Props {
  title: string;
  subtitle: string;
  ctaText: string;
  ctaLink: string;
  backgroundImage?: string;
}

const { title, subtitle, ctaText, ctaLink, backgroundImage } = Astro.props;
const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);
---

<section
  class="relative min-h-screen flex items-center justify-center overflow-hidden"
>
  <!-- Background -->
  <div class="absolute inset-0 z-0">
    {
      backgroundImage ? (
        <img
          src={backgroundImage}
          alt="Background"
          class="w-full h-full object-cover opacity-20"
        />
      ) : (
        <div class="w-full h-full bg-gradient-to-br from-primary-900 via-dark-100 to-secondary-900" />
      )
    }
    <div class="absolute inset-0 bg-black/40"></div>
  </div>

  <!-- Animated Background Elements -->
  <div class="absolute inset-0 z-0">
    <div
      class="absolute top-1/4 left-1/4 w-72 h-72 bg-primary-600/10 rounded-full blur-3xl animate-float"
    >
    </div>
    <div
      class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-secondary-500/10 rounded-full blur-3xl animate-float"
      style="animation-delay: -1s;"
    >
    </div>
    <div
      class="absolute top-1/2 left-1/2 w-48 h-48 bg-accent-500/10 rounded-full blur-3xl animate-float"
      style="animation-delay: -2s;"
    >
    </div>
  </div>

  <!-- Content -->
  <div class="relative z-10 container-width px-4 sm:px-6 lg:px-8 text-center">
    <!-- Logo Animation -->
    <div class="mb-8 animate-fade-in">
      <img
        src="/impactadvocacydfwlogo.png"
        alt="Impact Advocacy Logo"
        class="h-24 w-auto mx-auto mb-6 hover-lift"
      />
    </div>

    <!-- Main Heading -->
    <h1
      class="text-4xl sm:text-5xl lg:text-7xl font-bold text-white mb-6 animate-fade-up text-shadow"
    >
      <span class="block">
        {title.split(" ").slice(0, 2).join(" ")}
      </span>
      <span class="block gradient-text">
        {title.split(" ").slice(2).join(" ")}
      </span>
    </h1>

    <!-- Subtitle -->
    <p
      class="text-xl sm:text-2xl lg:text-3xl text-white/90 mb-8 max-w-4xl mx-auto animate-fade-up text-shadow"
      style="animation-delay: 0.2s;"
    >
      {subtitle}
    </p>

    <!-- CTA Button -->
    <div class="animate-fade-up" style="animation-delay: 0.4s;">
      <a
        href={ctaLink}
        class="inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-primary-600 hover:bg-primary-700 rounded-2xl transition-all duration-300 hover:scale-105 hover:shadow-2xl neon-glow group"
      >
        {ctaText}
        <svg
          class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
        </svg>
      </a>
    </div>
  </div>
</section>

<style>
  @keyframes fadeUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-up {
    animation: fadeUp 0.8s ease-out forwards;
    opacity: 0;
  }
</style>
