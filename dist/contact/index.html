<!DOCTYPE html><html lang="en" class="dark" data-astro-cid-37fxchfa> <head><meta charset="UTF-8"><meta name="description" content="Contact Impact Advocacy for financial literacy workshops, community empowerment services, and strategic consulting. Serving Latino families in Dallas, Fort Worth, Irving, and the DFW area."><meta name="viewport" content="width=device-width, initial-scale=1.0"><link rel="icon" type="image/png" href="/impactadvocacydfwlogo.png"><meta name="generator" content="Astro v5.12.5"><!-- Canonical URL --><link rel="canonical" href="https://impactadvocacy.info/contact/"><!-- Hreflang tags --><link rel="alternate" hreflang="en" href="https://impactadvocacy.info/"><link rel="alternate" hreflang="es" href="https://impactadvocacy.info/es/"><link rel="alternate" hreflang="x-default" href="https://impactadvocacy.info/"><!-- SEO Meta Tags --><title>Contact | Impact Advocacy</title><meta name="description" content="Contact Impact Advocacy for financial literacy workshops, community empowerment services, and strategic consulting. Serving Latino families in Dallas, Fort Worth, Irving, and the DFW area."><meta name="keywords" content="Latino financial literacy, DFW community empowerment, bilingual advocacy, Dallas financial education, real estate guidance, strategic consulting"><meta name="author" content="Impact Advocacy"><meta name="robots" content="index, follow"><!-- Open Graph / Facebook --><meta property="og:type" content="website"><meta property="og:url" content="https://impactadvocacy.info/contact/"><meta property="og:title" content="Contact | Impact Advocacy"><meta property="og:description" content="Contact Impact Advocacy for financial literacy workshops, community empowerment services, and strategic consulting. Serving Latino families in Dallas, Fort Worth, Irving, and the DFW area."><meta property="og:image" content="https://impactadvocacy.info/impactadvocacydfwlogo.png"><meta property="og:locale" content="en_US"><!-- Twitter --><meta property="twitter:card" content="summary_large_image"><meta property="twitter:url" content="https://impactadvocacy.info/contact/"><meta property="twitter:title" content="Contact | Impact Advocacy"><meta property="twitter:description" content="Contact Impact Advocacy for financial literacy workshops, community empowerment services, and strategic consulting. Serving Latino families in Dallas, Fort Worth, Irving, and the DFW area."><meta property="twitter:image" content="https://impactadvocacy.info/impactadvocacydfwlogo.png"><!-- JSON-LD Schema --><script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "Impact Advocacy",
      "url": "https://impactadvocacy.info",
      "logo": "https://i.ytimg.com/vi/xvZPoWpWok8/maxresdefault.jpg?sqp=-oaymwEmCIAKENAF8quKqQMa8AEB-AH-CYAC0AWKAgwIABABGGUgZShlMA8=&rs=AOn4CLBvN7x4_4YheTQd-_T94e_oS_W7Ww",
      "description": "Empowering Latino communities through financial literacy, education, and strategic advocacy in the Dallas-Fort Worth area.",
      "sameAs": [
        "https://facebook.com/impactadvocacy",
        "https://instagram.com/impactadvocacy", 
        "https://twitter.com/impactadvocacy"
      ],
      "address": {
        "@type": "PostalAddress",
        "addressRegion": "TX",
        "addressCountry": "US"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "******-827-0748",
        "email": "<EMAIL>",
        "contactType": "customer service"
      }
    }
  </script><!-- Fonts --><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><!-- Global Styles --><link rel="stylesheet" href="/_astro/about.Bg735U8Q.css">
<style>html{scroll-behavior:smooth}.toggle-container[data-astro-cid-lb7h3eps]{position:relative}.toggle-button[data-astro-cid-lb7h3eps]{position:relative;z-index:2}.toggle-button[data-astro-cid-lb7h3eps].active{background:linear-gradient(135deg,#3b82f6,#1d4ed8);box-shadow:0 4px 15px #3b82f64d}.toggle-button[data-astro-cid-lb7h3eps]:not(.active):hover{background:#ffffff1a}
</style></head> <body class="min-h-screen bg-dark-200 text-white" data-astro-cid-37fxchfa>  <header class="fixed top-0 left-0 right-0 z-50 glass-card border-b border-white/10"> <nav class="container-width px-4 sm:px-6 lg:px-8"> <div class="flex items-center justify-between h-16"> <!-- Logo --> <a href="/" class="flex items-center space-x-3 hover-lift"> <img src="/impactadvocacydfwlogo.png" alt="Impact Advocacy Logo" class="h-10 w-auto"> <span class="hidden sm:block text-xl font-bold gradient-text">
Impact Advocacy
</span> </a> <!-- Desktop Navigation --> <div class="hidden md:flex items-center space-x-8"> <a href="/" class="text-sm font-medium transition-colors duration-300 hover:text-primary-400 text-white"> Home </a> <a href="/about" class="text-sm font-medium text-white transition-colors duration-300 hover:text-primary-400"> About </a> <a href="/services" class="text-sm font-medium text-white transition-colors duration-300 hover:text-primary-400"> Services </a> <a href="/events" class="text-sm font-medium text-white transition-colors duration-300 hover:text-primary-400"> Events </a> <a href="/contact" class="text-sm font-medium text-white transition-colors duration-300 hover:text-primary-400"> Contact </a> <!-- Language Toggle --> <div class="language-toggle" data-astro-cid-lb7h3eps> <div class="flex items-center glass-card rounded-full p-1 toggle-container" data-astro-cid-lb7h3eps> <a href="/contact/" class="px-3 py-1 text-sm font-medium rounded-full transition-all duration-300 toggle-button bg-primary-600 text-white shadow-lg active" data-lang="en" data-astro-cid-lb7h3eps>
EN
</a> <a href="/es/contact/" class="px-3 py-1 text-sm font-medium rounded-full transition-all duration-300 toggle-button text-white/70 hover:text-white" data-lang="es" data-astro-cid-lb7h3eps>
ES
</a> </div> </div>  </div> <!-- Mobile menu button --> <div class="md:hidden flex items-center space-x-4"> <div class="language-toggle" data-astro-cid-lb7h3eps> <div class="flex items-center glass-card rounded-full p-1 toggle-container" data-astro-cid-lb7h3eps> <a href="/contact/" class="px-3 py-1 text-sm font-medium rounded-full transition-all duration-300 toggle-button bg-primary-600 text-white shadow-lg active" data-lang="en" data-astro-cid-lb7h3eps>
EN
</a> <a href="/es/contact/" class="px-3 py-1 text-sm font-medium rounded-full transition-all duration-300 toggle-button text-white/70 hover:text-white" data-lang="es" data-astro-cid-lb7h3eps>
ES
</a> </div> </div>  <button type="button" class="mobile-menu-button p-2 rounded-lg glass-card" aria-label="Toggle mobile menu"> <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path> </svg> </button> </div> </div> <!-- Mobile Navigation --> <div class="mobile-menu hidden md:hidden border-t border-white/10 pt-4 pb-4"> <div class="space-y-4"> <a href="/" class="block text-base font-medium text-white hover:text-primary-400 transition-colors duration-300"> Home </a> <a href="/about" class="block text-base font-medium text-white hover:text-primary-400 transition-colors duration-300"> About </a> <a href="/services" class="block text-base font-medium text-white hover:text-primary-400 transition-colors duration-300"> Services </a> <a href="/events" class="block text-base font-medium text-white hover:text-primary-400 transition-colors duration-300"> Events </a> <a href="/contact" class="block text-base font-medium text-white hover:text-primary-400 transition-colors duration-300"> Contact </a> </div> </div> </nav> </header> <script type="module">document.addEventListener("DOMContentLoaded",()=>{const e=document.querySelector(".mobile-menu-button"),t=document.querySelector(".mobile-menu");e&&t&&e.addEventListener("click",()=>{t.classList.toggle("hidden")})});</script> <main> <!-- Hero Section --> <section class="relative min-h-[50vh] flex items-center justify-center overflow-hidden"> <div class="absolute inset-0 bg-gradient-to-br from-primary-900 via-dark-100 to-secondary-900"></div> <div class="absolute inset-0"> <div class="absolute top-1/4 left-1/4 w-72 h-72 bg-primary-600/10 rounded-full blur-3xl animate-float"></div> <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-secondary-500/10 rounded-full blur-3xl animate-float" style="animation-delay: -1s;"></div> </div> <div class="relative z-10 container-width px-4 sm:px-6 lg:px-8 text-center"> <h1 class="text-5xl lg:text-7xl font-bold text-white mb-6 animate-fade-up">
Let's Build Something <span class="gradient-text">That Lasts</span> </h1> <p class="text-xl lg:text-2xl text-white/80 max-w-3xl mx-auto animate-fade-up" style="animation-delay: 0.2s;">
Start your journey toward financial empowerment with Impact Advocacy
</p> </div> </section> <!-- Contact Form Section --> <section class="section-padding"> <div class="container-width"> <div class="max-w-4xl mx-auto"> <!-- Header --> <div class="text-center mb-12"> <h2 class="text-4xl lg:text-5xl font-bold text-white mb-6"> Let&#39;s Build Something <span class="gradient-text"> That Lasts </span> </h2> <p class="text-xl text-white/70"> Get in touch with us to start your journey toward financial empowerment. </p> </div> <div class="grid grid-cols-1 lg:grid-cols-2 gap-12"> <!-- Contact Form --> <div class="glass-card p-8"> <h3 class="text-2xl font-bold text-white mb-6"> Send a Message </h3> <form class="contact-form space-y-6" action="https://formspree.io/f/mrbzgjvl" method="POST"> <!-- Name Field --> <div> <label for="name" class="block text-sm font-medium text-white/80 mb-2"> Name *
</label> <input type="text" id="name" name="name" required class="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300" placeholder="Your full name"> </div> <!-- Email Field --> <div> <label for="email" class="block text-sm font-medium text-white/80 mb-2"> Email *
</label> <input type="email" id="email" name="email" required class="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300" placeholder="<EMAIL>"> </div> <!-- Phone Field (Optional) --> <div> <label for="phone" class="block text-sm font-medium text-white/80 mb-2"> Phone (Optional)
</label> <input type="tel" id="phone" name="phone" class="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300" placeholder="(*************"> </div> <!-- Service Interest --> <div> <label for="service" class="block text-sm font-medium text-white/80 mb-2"> Service of Interest </label> <select id="service" name="service" class="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"> <option value="" class="bg-dark-100"> Select a service </option> <option value="financial-literacy" class="bg-dark-100"> Financial Literacy Training </option> <option value="community-empowerment" class="bg-dark-100"> Community Empowerment </option> <option value="strategic-consulting" class="bg-dark-100"> Strategic Consulting </option> <option value="real-estate" class="bg-dark-100"> Real Estate Guidance </option> <option value="other" class="bg-dark-100"> Other </option> </select> </div> <!-- Message Field --> <div> <label for="message" class="block text-sm font-medium text-white/80 mb-2"> Message *
</label> <textarea id="message" name="message" rows="5" required class="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300 resize-vertical" placeholder="Tell us how we can help you..."></textarea> </div> <!-- Hidden Honeypot for Spam Protection --> <input type="text" name="_gotcha" style="display:none !important"> <!-- Submit Button --> <button type="submit" class="w-full bg-primary-600 hover:bg-primary-700 text-white font-semibold py-4 px-6 rounded-lg transition-all duration-300 hover:scale-105 neon-glow focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-dark-200"> Send Message </button> </form> </div> <!-- Contact Information --> <div class="space-y-8"> <!-- Contact Details --> <div class="glass-card p-8"> <h3 class="text-2xl font-bold text-white mb-6"> Contact Information </h3> <div class="space-y-6"> <!-- Email --> <div class="flex items-center"> <div class="p-3 bg-primary-600/20 rounded-lg mr-4"> <svg class="w-6 h-6 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path> </svg> </div> <div> <p class="text-white/70 text-sm">Email</p> <a href="mailto:<EMAIL>" class="text-white hover:text-primary-400 transition-colors">
<EMAIL>
</a> </div> </div> <!-- Phone --> <div class="flex items-center"> <div class="p-3 bg-secondary-600/20 rounded-lg mr-4"> <svg class="w-6 h-6 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path> </svg> </div> <div> <p class="text-white/70 text-sm"> Phone </p> <a href="tel:+19498270748" class="text-white hover:text-secondary-400 transition-colors">
+1 (*************
</a> </div> </div> <!-- Location --> <div class="flex items-center"> <div class="p-3 bg-accent-600/20 rounded-lg mr-4"> <svg class="w-6 h-6 text-accent-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path> </svg> </div> <div> <p class="text-white/70 text-sm"> Location </p> <p class="text-white"> Dallas-Fort Worth Area, Texas </p> </div> </div> </div> </div> <!-- Values Card --> <div class="glass-card p-8"> <h3 class="text-2xl font-bold text-white mb-6"> Our Values </h3> <div class="space-y-4"> <div class="flex items-center"> <div class="w-3 h-3 bg-primary-600 rounded-full mr-3"></div> <span class="text-white"> <strong class="text-primary-400">Confianza</strong>  (Trust) </span> </div> <div class="flex items-center"> <div class="w-3 h-3 bg-secondary-600 rounded-full mr-3"></div> <span class="text-white"> <strong class="text-secondary-400">Respeto</strong>  (Respect) </span> </div> <div class="flex items-center"> <div class="w-3 h-3 bg-accent-600 rounded-full mr-3"></div> <span class="text-white"> <strong class="text-accent-400">Acción</strong>  (Action) </span> </div> </div> </div> </div> </div> </div> </div> </section> <script type="module">document.addEventListener("DOMContentLoaded",()=>{const t=document.querySelector(".contact-form");t&&t.addEventListener("submit",async s=>{s.preventDefault();const e=t.querySelector('button[type="submit"]'),r=e.textContent;e.disabled=!0,e.textContent="Sending...";try{if((await fetch(t.action,{method:"POST",body:new FormData(t),headers:{Accept:"application/json"}})).ok)e.textContent="✓ Sent!",e.classList.add("bg-green-600","hover:bg-green-700"),e.classList.remove("bg-primary-600","hover:bg-primary-700"),t.reset(),setTimeout(()=>{e.disabled=!1,e.textContent=r,e.classList.remove("bg-green-600","hover:bg-green-700"),e.classList.add("bg-primary-600","hover:bg-primary-700")},3e3);else throw new Error("Form submission failed")}catch{e.textContent="✗ Error - Try Again",e.classList.add("bg-red-600","hover:bg-red-700"),e.classList.remove("bg-primary-600","hover:bg-primary-700"),setTimeout(()=>{e.disabled=!1,e.textContent=r,e.classList.remove("bg-red-600","hover:bg-red-700"),e.classList.add("bg-primary-600","hover:bg-primary-700")},3e3)}})});</script> <!-- Direct Contact Methods --> <section class="section-padding bg-dark-100"> <div class="container-width"> <div class="text-center mb-16"> <h2 class="text-4xl lg:text-5xl font-bold text-white mb-6">
Other Ways to <span class="gradient-text">Connect</span> </h2> <p class="text-xl text-white/70">
Reach out through your preferred communication method
</p> </div> <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"> <!-- Direct Call --> <div class="glass-card p-8 text-center hover-lift"> <div class="w-16 h-16 bg-primary-600/20 rounded-full flex items-center justify-center mx-auto mb-6"> <svg class="w-8 h-8 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path> </svg> </div> <h3 class="text-xl font-bold text-white mb-4">Call Us Directly</h3> <p class="text-white/70 mb-6">
Speak with our team for immediate assistance and consultation scheduling.
</p> <a href="tel:+19498270748" class="inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-all duration-300">
(*************
<svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path> </svg> </a> </div> <!-- WhatsApp --> <div class="glass-card p-8 text-center hover-lift"> <div class="w-16 h-16 bg-green-600/20 rounded-full flex items-center justify-center mx-auto mb-6"> <svg class="w-8 h-8 text-green-400" fill="currentColor" viewBox="0 0 24 24"> <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"></path> </svg> </div> <h3 class="text-xl font-bold text-white mb-4">WhatsApp Chat</h3> <p class="text-white/70 mb-6">
Send us a message on WhatsApp for quick responses and easy communication.
</p> <a href="https://wa.me/19498270748" target="_blank" rel="noopener noreferrer" class="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-all duration-300">
Chat on WhatsApp
<svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path> </svg> </a> </div> <!-- Email --> <div class="glass-card p-8 text-center hover-lift"> <div class="w-16 h-16 bg-secondary-600/20 rounded-full flex items-center justify-center mx-auto mb-6"> <svg class="w-8 h-8 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path> </svg> </div> <h3 class="text-xl font-bold text-white mb-4">Send Email</h3> <p class="text-white/70 mb-6">
Email us with detailed questions or to schedule a consultation at your convenience.
</p> <a href="mailto:<EMAIL>" class="inline-flex items-center px-6 py-3 bg-secondary-600 hover:bg-secondary-700 text-white font-medium rounded-lg transition-all duration-300">
Send Email
<svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path> </svg> </a> </div> </div> </div> </section> <!-- Team Contact Cards --> <section class="section-padding"> <div class="container-width"> <div class="text-center mb-16"> <h2 class="text-4xl lg:text-5xl font-bold text-white mb-6">
Connect with Our <span class="gradient-text">Team</span> </h2> <p class="text-xl text-white/70">
Reach out to specific team members based on your needs
</p> </div> <div class="grid grid-cols-1 md:grid-cols-3 gap-8"> <!-- Tessie Santiago --> <div class="glass-card p-6 text-center hover-lift"> <div class="w-24 h-24 mx-auto mb-4 rounded-full overflow-hidden border-2 border-primary-600/30"> <img src="/tessiesantiago.jpg" alt="Tessie Santiago" class="w-full h-full object-cover"> </div> <h3 class="text-xl font-bold text-white mb-2">Tessie Santiago</h3> <p class="text-primary-400 mb-4">Co-founder & President</p> <p class="text-white/70 text-sm mb-6">
Community empowerment, financial literacy workshops, media relations
</p> <div class="flex justify-center space-x-3"> <a href="tel:+14697089270" class="p-2 bg-primary-600/20 rounded-lg hover:bg-primary-600/30 transition-colors" aria-label="Call Tessie"> <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path> </svg> </a> <a href="https://wa.me/14697089270" target="_blank" rel="noopener noreferrer" class="p-2 bg-green-600/20 rounded-lg hover:bg-green-600/30 transition-colors" aria-label="WhatsApp Tessie"> <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 24 24"> <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"></path> </svg> </a> </div> </div> <!-- Jackie Guevara --> <div class="glass-card p-6 text-center hover-lift"> <div class="w-24 h-24 mx-auto mb-4 rounded-full overflow-hidden border-2 border-secondary-600/30"> <img src="/jackieguevara.jpg" alt="Jackie Guevara" class="w-full h-full object-cover"> </div> <h3 class="text-xl font-bold text-white mb-2">Jackie Guevara</h3> <p class="text-secondary-400 mb-4">Co-founder & Strategic Consultant</p> <p class="text-white/70 text-sm mb-6">
Strategic consulting, business development, community empowerment
</p> <div class="flex justify-center space-x-3"> <a href="tel:+***********" class="p-2 bg-secondary-600/20 rounded-lg hover:bg-secondary-600/30 transition-colors" aria-label="Call Jackie"> <svg class="w-4 h-4 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path> </svg> </a> <a href="https://wa.me/***********" target="_blank" rel="noopener noreferrer" class="p-2 bg-green-600/20 rounded-lg hover:bg-green-600/30 transition-colors" aria-label="WhatsApp Jackie"> <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 24 24"> <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"></path> </svg> </a> <a href="https://jackieguevara.com/" target="_blank" rel="noopener noreferrer" class="p-2 bg-secondary-600/20 rounded-lg hover:bg-secondary-600/30 transition-colors" aria-label="Jackie's Website"> <svg class="w-4 h-4 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path> </svg> </a> </div> </div> <!-- Emanuel Ibarra --> <div class="glass-card p-6 text-center hover-lift"> <div class="w-24 h-24 mx-auto mb-4 rounded-full overflow-hidden border-2 border-accent-600/30"> <img src="/dfwrealtor.jpeg" alt="Emanuel Ibarra" class="w-full h-full object-cover"> </div> <h3 class="text-xl font-bold text-white mb-2">Emanuel Ibarra</h3> <p class="text-accent-400 mb-4">Real Estate Specialist</p> <p class="text-white/70 text-sm mb-6">
Real estate guidance, housing assistance, investment strategies
</p> <div class="flex justify-center space-x-3"> <a href="tel:+12148152742" class="p-2 bg-accent-600/20 rounded-lg hover:bg-accent-600/30 transition-colors" aria-label="Call Emanuel"> <svg class="w-4 h-4 text-accent-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path> </svg> </a> <a href="https://wa.me/12148152742" target="_blank" rel="noopener noreferrer" class="p-2 bg-green-600/20 rounded-lg hover:bg-green-600/30 transition-colors" aria-label="WhatsApp Emanuel"> <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 24 24"> <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"></path> </svg> </a> <a href="https://emanuelibarra.com/" target="_blank" rel="noopener noreferrer" class="p-2 bg-accent-600/20 rounded-lg hover:bg-accent-600/30 transition-colors" aria-label="Emanuel's Website"> <svg class="w-4 h-4 text-accent-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path> </svg> </a> </div> </div> </div> </div> </section> </main> <footer class="bg-dark-100 border-t border-white/10"> <div class="container-width section-padding"> <div class="grid grid-cols-1 md:grid-cols-4 gap-8"> <!-- Brand Section --> <div class="md:col-span-2"> <div class="flex items-center space-x-3 mb-4"> <img src="/impactadvocacydfwlogo.png" alt="Impact Advocacy Logo" class="h-12 w-auto"> <span class="text-2xl font-bold gradient-text">
Impact Advocacy
</span> </div> <p class="text-white/70 mb-6 max-w-md"> Empowering Latino communities through financial literacy, education, and strategic advocacy in the Dallas-Fort Worth area. </p> <!-- Social Media Links --> <div class="flex space-x-4"> <a href="https://facebook.com/impactadvocacy" target="_blank" rel="noopener noreferrer" class="p-2 glass-card rounded-lg hover:bg-white/10 transition-all duration-300" aria-label="Facebook"> <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"> <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"></path> </svg> </a> <a href="https://instagram.com/impactadvocacy" target="_blank" rel="noopener noreferrer" class="p-2 glass-card rounded-lg hover:bg-white/10 transition-all duration-300" aria-label="Instagram"> <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"> <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.802 8.196 6.953 7.706 8.25 7.706s2.448.49 3.323 1.297c.876.876 1.366 2.027 1.366 3.324s-.49 2.448-1.366 3.323c-.875.807-2.026 1.297-3.323 1.297z"></path> </svg> </a> <a href="https://tiktok.com/@impactadvocacy" target="_blank" rel="noopener noreferrer" class="p-2 glass-card rounded-lg hover:bg-white/10 transition-all duration-300" aria-label="TikTok"> <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"> <path d="M19.59 6.69a4.83 4.83 0 01-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 01-5.2 1.74 2.89 2.89 0 012.31-4.64 2.93 2.93 0 01.88.13V9.4a6.84 6.84 0 00-.88-.05A6.33 6.33 0 005 20.1a6.34 6.34 0 0010.86-4.43v-7a8.16 8.16 0 004.77 1.52v-3.4a4.85 4.85 0 01-1-.1z"></path> </svg> </a> <a href="https://twitter.com/impactadvocacy" target="_blank" rel="noopener noreferrer" class="p-2 glass-card rounded-lg hover:bg-white/10 transition-all duration-300" aria-label="Twitter/X"> <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"> <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"></path> </svg> </a> </div> </div> <!-- Quick Links --> <div> <h3 class="text-lg font-semibold text-white mb-4"> Quick Links </h3> <ul class="space-y-2"> <li> <a href="/" class="text-white/70 hover:text-white transition-colors duration-300"> Home </a> </li> <li> <a href="/about" class="text-white/70 hover:text-white transition-colors duration-300"> About </a> </li> <li> <a href="/services" class="text-white/70 hover:text-white transition-colors duration-300"> Services </a> </li> <li> <a href="/events" class="text-white/70 hover:text-white transition-colors duration-300"> Events </a> </li> <li> <a href="/contact" class="text-white/70 hover:text-white transition-colors duration-300"> Contact </a> </li> </ul> </div> <!-- Contact Info --> <div> <h3 class="text-lg font-semibold text-white mb-4"> Contact </h3> <ul class="space-y-2 text-white/70"> <li class="flex items-center"> <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20"> <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path> <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path> </svg> <a href="mailto:<EMAIL>" class="hover:text-white transition-colors">
<EMAIL>
</a> </li> <li class="flex items-center"> <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20"> <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path> </svg> <a href="tel:+19498270748" class="hover:text-white transition-colors">
+1 (*************
</a> </li> <li class="flex items-start"> <svg class="w-4 h-4 mr-2 mt-1" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path> </svg> <span> Serving the Dallas-Fort Worth Area </span> </li> </ul> </div> </div> <!-- Bottom Section --> <div class="border-t border-white/10 mt-8 pt-8"> <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0"> <div class="text-white/70 text-sm"> <p>© 2025 Impact Advocacy Inc. All rights reserved.</p> <p class="mt-1">Educational content. Not professional financial or legal advice.</p> </div> <div class="flex items-center space-x-4 text-sm text-white/70"> <span> Powered by <a href="https://elgatoai.com" target="_blank" rel="noopener noreferrer" class="text-primary-400 hover:text-primary-300">
El Gato AI
</a> </span> </div> </div> </div> </div> </footer>  <!-- WhatsApp Float Button --> <a href="https://wa.me/19498270748" target="_blank" rel="noopener noreferrer" class="fixed bottom-6 left-6 z-50 bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-2xl transition-all duration-300 hover:scale-110 pulse-animation" aria-label="Contact us on WhatsApp" data-astro-cid-37fxchfa> <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-37fxchfa> <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488" data-astro-cid-37fxchfa></path> </svg> </a> </body></html> <script type="module" src="/_astro/contact.astro_astro_type_script_index_0_lang.CHbTuhHD.js"></script>