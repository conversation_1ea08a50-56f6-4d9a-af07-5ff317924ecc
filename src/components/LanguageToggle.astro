---
import { getLangFromUrl } from "../utils/i18n";

const currentLang = getLangFromUrl(Astro.url);
const currentPath = Astro.url.pathname;

// Generate URLs for both languages
let englishUrl, spanishUrl;

if (currentLang === "es") {
  // Currently on Spanish page
  englishUrl = currentPath.replace(/^\/es/, "") || "/";
  spanishUrl = currentPath;
} else {
  // Currently on English page
  englishUrl = currentPath;
  spanishUrl = "/es" + currentPath;
}
---

<div class="language-toggle">
  <div
    class={`flex items-center glass-card rounded-full p-1 toggle-container ${
      currentLang === "en" ? "en-active" : "es-active"
    }`}
  >
    <a
      href={englishUrl}
      class={`toggle-button ${currentLang === "en" ? "active" : ""}`}
      data-lang="en"
    >
      EN
    </a>
    <a
      href={spanishUrl}
      class={`toggle-button ${currentLang === "es" ? "active" : ""}`}
      data-lang="es"
    >
      ES
    </a>
  </div>
</div>

<style>
  .language-toggle {
    display: flex;
    justify-content: flex-end;
    padding: 0.5rem;
  }

  .toggle-container {
    position: relative;
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 9999px;
    padding: 0.25rem;
    display: flex;
    gap: 0.25rem;
  }

  .toggle-button {
    position: relative;
    z-index: 2;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 9999px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    color: rgba(255, 255, 255, 0.7);
    border: none;
    background: transparent;
    cursor: pointer;
  }

  .toggle-button.active {
    background: linear-gradient(135deg, #16a34a, #15803d);
    color: white;
    box-shadow:
      0 4px 15px rgba(22, 163, 74, 0.3),
      0 2px 4px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  .toggle-button:not(.active):hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateY(-0.5px);
  }

  /* Smooth sliding background effect */
  .toggle-container::before {
    content: "";
    position: absolute;
    top: 0.25rem;
    left: 0.25rem;
    width: calc(50% - 0.125rem);
    height: calc(100% - 0.5rem);
    background: linear-gradient(135deg, #16a34a, #15803d);
    border-radius: 9999px;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
    opacity: 0;
  }

  .toggle-container.es-active::before {
    transform: translateX(100%);
    opacity: 1;
  }

  .toggle-container.en-active::before {
    transform: translateX(0);
    opacity: 1;
  }
</style>
