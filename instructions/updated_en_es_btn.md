### BEGIN INSTRUCTIONS

Implement a language toggle switch (EN/ES) that controls content visibility, without dependencies, in a clean HTML/CSS/JS way.


🧩 1. HTML – Insert This Anywhere in Your Navbar or Top Bar

``` html
<div id="lang-toggle" class="lang-toggle">
  <button id="en-btn" class="lang-btn active">EN</button>
  <button id="es-btn" class="lang-btn">ES</button>
</div>
``` 

🎨 2. CSS – Add This to Your Main Stylesheet
Adapt colors to match your Impact Advocacy theme if needed.

```css
.lang-toggle {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  padding: 0.5rem;
}

.lang-btn {
  padding: 0.4rem 0.8rem;
  border: none;
  background-color: #1e293b;
  color: white;
  font-weight: bold;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.lang-btn.active {
  background-color: #16a34a; /* Accent green when active */
}

.lang-btn:hover {
  background-color: #0f766e;
}
```


⚙️ 3. JS – Insert Before Closing </body>
Put this inline or in a small <script> tag at the end of your page.

```html
<script>
  const enBtn = document.getElementById("en-btn");
  const esBtn = document.getElementById("es-btn");

  const updateLanguage = (lang) => {
    document.querySelectorAll("[data-lang]").forEach(el => {
      el.style.display = (el.dataset.lang === lang) ? "" : "none";
    });

    // Toggle active button
    enBtn.classList.toggle("active", lang === "en");
    esBtn.classList.toggle("active", lang === "es");

    // Optionally save language in localStorage
    localStorage.setItem("lang", lang);
  };

  // Button Events
  enBtn.onclick = () => updateLanguage("en");
  esBtn.onclick = () => updateLanguage("es");

  // On Load: apply saved language or default to EN
  updateLanguage(localStorage.getItem("lang") || "en");
</script>
```

### END INSTRUCTIONS