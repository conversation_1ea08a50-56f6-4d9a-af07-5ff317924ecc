"use strict";var l=Object.defineProperty;var d=Object.getOwnPropertyDescriptor;var p=Object.getOwnPropertyNames;var N=Object.prototype.hasOwnProperty;var u=(o,e)=>{for(var t in e)l(o,t,{get:e[t],enumerable:!0})},f=(o,e,t,a)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of p(e))!N.call(o,r)&&r!==t&&l(o,r,{get:()=>e[r],enumerable:!(a=d(e,r))||a.enumerable});return o};var h=o=>f(l({},"__esModule",{value:!0}),o);var k={};u(k,{is:()=>s,serialize:()=>g,walk:()=>y});module.exports=h(k);function n(o){return e=>e.type===o}var s={parent(o){return Array.isArray(o.children)},literal(o){return typeof o.value=="string"},tag(o){return o.type==="element"||o.type==="custom-element"||o.type==="component"||o.type==="fragment"},whitespace(o){return o.type==="text"&&o.value.trim().length===0},root:n("root"),element:n("element"),customElement:n("custom-element"),component:n("component"),fragment:n("fragment"),expression:n("expression"),text:n("text"),doctype:n("doctype"),comment:n("comment"),frontmatter:n("frontmatter")},m=class{constructor(e){this.callback=e}async visit(e,t,a){if(await this.callback(e,t,a),s.parent(e)){let r=[];for(let i=0;i<e.children.length;i++){let c=e.children[i];r.push(this.callback(c,e,i))}await Promise.all(r)}}};function y(o,e){new m(e).visit(o)}function x(o){let e="";for(let t of o.attributes)switch(e+=" ",t.kind){case"empty":{e+=`${t.name}`;break}case"expression":{e+=`${t.name}={${t.value}}`;break}case"quoted":{e+=`${t.name}=${t.raw}`;break}case"template-literal":{e+=`${t.name}=\`${t.value}\``;break}case"shorthand":{e+=`{${t.name}}`;break}case"spread":{e+=`{...${t.name}}`;break}}return e}function g(o,e={selfClose:!0}){let t="";function a(r){if(s.root(r))for(let i of r.children)a(i);else if(s.frontmatter(r))t+=`---${r.value}---

`;else if(s.comment(r))t+=`<!--${r.value}-->`;else if(s.expression(r)){t+="{";for(let i of r.children)a(i);t+="}"}else if(s.literal(r))t+=r.value;else if(s.tag(r))if(t+=`<${r.name}`,t+=x(r),r.children.length===0&&e.selfClose)t+=" />";else{t+=">";for(let i of r.children)a(i);t+=`</${r.name}>`}}return a(o),t}0&&(module.exports={is,serialize,walk});
