
---
import { getLangFromUrl } from '../utils/i18n';

const currentLang = getLangFromUrl(Astro.url);

const teamMembers = [
  {
    name: '<PERSON><PERSON>',
    title: currentLang === 'es' ? 'Co-fundadora y Presidenta' : 'Co-founder & President',
    bio: currentLang === 'es' 
      ? 'Personalidad televisiva de Univision y ex Presidenta de la Cámara de Dallas con más de 20 años de experiencia en empoderamiento comunitario.'
      : 'TV personality on Univision and former Dallas Chamber President with 20+ years of community empowerment experience.',
    photo: '/tessiesantiago.jpg',
    phone: '******-708-9270',
    whatsapp: 'https://wa.me/***********',
    email: '<EMAIL>',
    linkedin: '#'
  },
  {
    name: '<PERSON>',
    title: currentLang === 'es' ? 'Co-fundadora y Consultora Estratégica' : 'Co-founder & Strategic Consultant',
    bio: currentLang === 'es'
      ? 'Consultora empresarial experimentada especializada en desarrollo comunitario y estrategias de crecimiento sostenible.'
      : 'Experienced business consultant specializing in community development and sustainable growth strategies.',
    photo: '/jackieguevara.jpg',
    phone: '******-998-1086',
    whatsapp: 'https://wa.me/***********',
    website: 'https://jackieguevara.com/'
  },
  {
    name: 'Emanuel Ibarra',
    title: currentLang === 'es' ? 'Especialista en Bienes Raíces' : 'Real Estate Specialist',
    bio: currentLang === 'es'
      ? 'Experto en bienes raíces del área de DFW, brindando orientación sobre inversiones inmobiliarias y oportunidades de vivienda.'
      : 'DFW area real estate expert providing guidance on real estate investments and housing opportunities.',
    photo: '/dfwrealtor.jpeg',
    phone: '******-815-2742',
    whatsapp: 'https://wa.me/***********',
    website: 'https://emanuelibarra.com/'
  }
];
---

<section class="section-padding bg-dark-100">
  <div class="container-width">
    <!-- Section Header -->
    <div class="text-center mb-16">
      <h2 class="text-4xl lg:text-5xl font-bold text-white mb-6">
        {currentLang === 'es' ? 'Conoce a' : 'Meet'} 
        <span class="gradient-text">
          {currentLang === 'es' ? 'Nuestro Equipo' : 'Our Team'}
        </span>
      </h2>
      <p class="text-xl text-white/70 max-w-3xl mx-auto">
        {currentLang === 'es' 
          ? 'Líderes experimentados dedicados a empoderar a las comunidades latinas en DFW.'
          : 'Experienced leaders dedicated to empowering Latino communities in DFW.'
        }
      </p>
    </div>

    <!-- Team Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {teamMembers.map((member, index) => (
        <div 
          class="relative glass-card p-0 overflow-hidden hover-lift animate-fade-up group team-card-enhanced"
          style={`animation-delay: ${index * 0.2}s;`}
        >
          <!-- Photo Section -->
          <div class="relative h-64 overflow-hidden">
            <img 
              src={member.photo}
              alt={member.name}
              class="w-full object-contain group-hover:scale-110 transition-transform duration-500"
            />
            <div class="absolute inset-0 bg-gradient-to-t from-dark-200 via-transparent to-transparent opacity-60"></div>
            <div class="absolute bottom-4 left-4 right-4">
              <h3 class="text-2xl font-bold text-white mb-1 title-hover-glow">{member.name}</h3>
              <p class="text-primary-400 font-medium text-sm">{member.title}</p>
            </div>
          </div>

          <!-- Content Section -->
          <div class="p-6">
            <p class="text-white/80 mb-6 leading-relaxed text-sm">{member.bio}</p>

            <!-- Contact Links -->
            <div class="flex justify-center space-x-3">
              {member.whatsapp && (
                <a 
                  href={member.whatsapp}
                  target="_blank"
                  rel="noopener noreferrer"
                  class="p-3 glass-card rounded-full hover:bg-green-500/20 transition-all duration-300 group/btn"
                  aria-label={`WhatsApp ${member.name}`}
                >
                  <svg class="w-5 h-5 text-green-400 group-hover/btn:scale-110 transition-transform" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                  </svg>
                </a>
              )}

              {member.email && (
                <a 
                  href={`mailto:${member.email}`}
                  class="p-3 glass-card rounded-full hover:bg-blue-500/20 transition-all duration-300 group/btn"
                  aria-label={`Email ${member.name}`}
                >
                  <svg class="w-5 h-5 text-blue-400 group-hover/btn:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                  </svg>
                </a>
              )}
              
              {member.website && (
                <a 
                  href={member.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  class="p-3 glass-card rounded-full hover:bg-primary-500/20 transition-all duration-300 group/btn"
                  aria-label={`Website ${member.name}`}
                >
                  <svg class="w-5 h-5 text-primary-400 group-hover/btn:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                  </svg>
                </a>
              )}
              
              <a 
                href={`tel:${member.phone}`}
                class="p-3 glass-card rounded-full hover:bg-secondary-500/20 transition-all duration-300 group/btn"
                aria-label={`Call ${member.name}`}
              >
                <svg class="w-5 h-5 text-secondary-400 group-hover/btn:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                </svg>
              </a>
            </div>
          </div>

          <!-- Hover Overlay Effect -->
          <div class="absolute inset-0 bg-gradient-to-t from-primary-600/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
        </div>
      ))}
    </div>
  </div>
</section>
