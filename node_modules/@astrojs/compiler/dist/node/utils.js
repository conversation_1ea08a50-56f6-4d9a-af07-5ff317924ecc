function n(o){return t=>t.type===o}var a={parent(o){return Array.isArray(o.children)},literal(o){return typeof o.value=="string"},tag(o){return o.type==="element"||o.type==="custom-element"||o.type==="component"||o.type==="fragment"},whitespace(o){return o.type==="text"&&o.value.trim().length===0},root:n("root"),element:n("element"),customElement:n("custom-element"),component:n("component"),fragment:n("fragment"),expression:n("expression"),text:n("text"),doctype:n("doctype"),comment:n("comment"),frontmatter:n("frontmatter")},l=class{constructor(t){this.callback=t}async visit(t,e,s){if(await this.callback(t,e,s),a.parent(t)){let r=[];for(let i=0;i<t.children.length;i++){let m=t.children[i];r.push(this.callback(m,t,i))}await Promise.all(r)}}};function N(o,t){new l(t).visit(o)}function c(o){let t="";for(let e of o.attributes)switch(t+=" ",e.kind){case"empty":{t+=`${e.name}`;break}case"expression":{t+=`${e.name}={${e.value}}`;break}case"quoted":{t+=`${e.name}=${e.raw}`;break}case"template-literal":{t+=`${e.name}=\`${e.value}\``;break}case"shorthand":{t+=`{${e.name}}`;break}case"spread":{t+=`{...${e.name}}`;break}}return t}function u(o,t={selfClose:!0}){let e="";function s(r){if(a.root(r))for(let i of r.children)s(i);else if(a.frontmatter(r))e+=`---${r.value}---

`;else if(a.comment(r))e+=`<!--${r.value}-->`;else if(a.expression(r)){e+="{";for(let i of r.children)s(i);e+="}"}else if(a.literal(r))e+=r.value;else if(a.tag(r))if(e+=`<${r.name}`,e+=c(r),r.children.length===0&&t.selfClose)e+=" />";else{e+=">";for(let i of r.children)s(i);e+=`</${r.name}>`}}return s(o),e}export{a as is,u as serialize,N as walk};
