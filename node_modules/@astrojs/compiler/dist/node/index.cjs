"use strict";var P=Object.create;var g=Object.defineProperty;var R=Object.getOwnPropertyDescriptor;var I=Object.getOwnPropertyNames;var A=Object.getPrototypeOf,E=Object.prototype.hasOwnProperty;var O=(r,n)=>{for(var s in n)g(r,s,{get:n[s],enumerable:!0})},T=(r,n,s,i)=>{if(n&&typeof n=="object"||typeof n=="function")for(let c of I(n))!E.call(r,c)&&c!==s&&g(r,c,{get:()=>n[c],enumerable:!(i=R(n,c))||i.enumerable});return r};var x=(r,n,s)=>(s=r!=null?P(A(r)):{},T(n||!r||!r.__esModule?g(s,"default",{value:r,enumerable:!0}):s,r)),C=r=>T(g({},"__esModule",{value:!0}),r);var B={};O(B,{compile:()=>L,convertToTSX:()=>F,parse:()=>V,teardown:()=>W,transform:()=>D});module.exports=C(B);var N=()=>typeof document>"u"?new URL("file:"+__filename).href:document.currentScript&&document.currentScript.src||new URL("main.js",document.baseURI).href,b=N();var S=require("fs"),j=require("url");var p=x(require("crypto"),1),v=x(require("fs"),1),_=require("util");globalThis.fs||Object.defineProperty(globalThis,"fs",{value:v.default});globalThis.process||Object.defineProperties(globalThis,"process",{value:process});globalThis.crypto||Object.defineProperty(globalThis,"crypto",{value:p.default.webcrypto?p.default.webcrypto:{getRandomValues(r){return p.default.randomFillSync(r)}}});globalThis.performance||Object.defineProperty(globalThis,"performance",{value:{now(){let[r,n]=process.hrtime();return r*1e3+n/1e6}}});var U=new _.TextEncoder("utf-8"),k=new _.TextDecoder("utf-8");var y=class{constructor(){this.argv=["js"],this.env={},this.exit=e=>{e!==0&&console.warn("exit code:",e)},this._exitPromise=new Promise(e=>{this._resolveExitPromise=e}),this._pendingEvent=null,this._scheduledTimeouts=new Map,this._nextCallbackTimeoutID=1;let n=(e,t)=>{this.mem.setUint32(e+0,t,!0),this.mem.setUint32(e+4,Math.floor(t/4294967296),!0)},s=e=>{let t=this.mem.getUint32(e+0,!0),a=this.mem.getInt32(e+4,!0);return t+a*4294967296},i=e=>{let t=this.mem.getFloat64(e,!0);if(t===0)return;if(!isNaN(t))return t;let a=this.mem.getUint32(e,!0);return this._values[a]},c=(e,t)=>{if(typeof t=="number"&&t!==0){if(isNaN(t)){this.mem.setUint32(e+4,2146959360,!0),this.mem.setUint32(e,0,!0);return}this.mem.setFloat64(e,t,!0);return}if(t===void 0){this.mem.setFloat64(e,0,!0);return}let l=this._ids.get(t);l===void 0&&(l=this._idPool.pop(),l===void 0&&(l=this._values.length),this._values[l]=t,this._goRefCounts[l]=0,this._ids.set(t,l)),this._goRefCounts[l]++;let m=0;switch(typeof t){case"object":t!==null&&(m=1);break;case"string":m=2;break;case"symbol":m=3;break;case"function":m=4;break}this.mem.setUint32(e+4,2146959360|m,!0),this.mem.setUint32(e,l,!0)},o=e=>{let t=s(e+0),a=s(e+8);return new Uint8Array(this._inst.exports.mem.buffer,t,a)},d=e=>{let t=s(e+0),a=s(e+8),l=new Array(a);for(let m=0;m<a;m++)l[m]=i(t+m*8);return l},f=e=>{let t=s(e+0),a=s(e+8);return k.decode(new DataView(this._inst.exports.mem.buffer,t,a))},u=Date.now()-performance.now();this.importObject={gojs:{"runtime.wasmExit":e=>{e>>>=0;let t=this.mem.getInt32(e+8,!0);this.exited=!0,delete this._inst,delete this._values,delete this._goRefCounts,delete this._ids,delete this._idPool,this.exit(t)},"runtime.wasmWrite":e=>{e>>>=0;let t=s(e+8),a=s(e+16),l=this.mem.getInt32(e+24,!0);v.default.writeSync(t,new Uint8Array(this._inst.exports.mem.buffer,a,l))},"runtime.resetMemoryDataView":e=>{e>>>=0,this.mem=new DataView(this._inst.exports.mem.buffer)},"runtime.nanotime1":e=>{e>>>=0,n(e+8,(u+performance.now())*1e6)},"runtime.walltime":e=>{e>>>=0;let t=new Date().getTime();n(e+8,t/1e3),this.mem.setInt32(e+16,t%1e3*1e6,!0)},"runtime.scheduleTimeoutEvent":e=>{e>>>=0;let t=this._nextCallbackTimeoutID;this._nextCallbackTimeoutID++,this._scheduledTimeouts.set(t,setTimeout(()=>{for(this._resume();this._scheduledTimeouts.has(t);)console.warn("scheduleTimeoutEvent: missed timeout event"),this._resume()},s(e+8)+1)),this.mem.setInt32(e+16,t,!0)},"runtime.clearTimeoutEvent":e=>{e>>>=0;let t=this.mem.getInt32(e+8,!0);clearTimeout(this._scheduledTimeouts.get(t)),this._scheduledTimeouts.delete(t)},"runtime.getRandomData":e=>{e>>>=0,globalThis.crypto.getRandomValues(o(e+8))},"syscall/js.finalizeRef":e=>{e>>>=0;let t=this.mem.getUint32(e+8,!0);if(this._goRefCounts[t]--,this._goRefCounts[t]===0){let a=this._values[t];this._values[t]=null,this._ids.delete(a),this._idPool.push(t)}},"syscall/js.stringVal":e=>{e>>>=0,c(e+24,f(e+8))},"syscall/js.valueGet":e=>{e>>>=0;let t=Reflect.get(i(e+8),f(e+16));e=this._inst.exports.getsp()>>>0,c(e+32,t)},"syscall/js.valueSet":e=>{e>>>=0,Reflect.set(i(e+8),f(e+16),i(e+32))},"syscall/js.valueDelete":e=>{e>>>=0,Reflect.deleteProperty(i(e+8),f(e+16))},"syscall/js.valueIndex":e=>{e>>>=0,c(e+24,Reflect.get(i(e+8),s(e+16)))},"syscall/js.valueSetIndex":e=>{e>>>=0,Reflect.set(i(e+8),s(e+16),i(e+24))},"syscall/js.valueCall":e=>{e>>>=0;try{let t=i(e+8),a=Reflect.get(t,f(e+16)),l=d(e+32),m=Reflect.apply(a,t,l);e=this._inst.exports.getsp()>>>0,c(e+56,m),this.mem.setUint8(e+64,1)}catch(t){e=this._inst.exports.getsp()>>>0,c(e+56,t),this.mem.setUint8(e+64,0)}},"syscall/js.valueInvoke":e=>{e>>>=0;try{let t=i(e+8),a=d(e+16),l=Reflect.apply(t,void 0,a);e=this._inst.exports.getsp()>>>0,c(e+40,l),this.mem.setUint8(e+48,1)}catch(t){e=this._inst.exports.getsp()>>>0,c(e+40,t),this.mem.setUint8(e+48,0)}},"syscall/js.valueNew":e=>{e>>>=0;try{let t=i(e+8),a=d(e+16),l=Reflect.construct(t,a);e=this._inst.exports.getsp()>>>0,c(e+40,l),this.mem.setUint8(e+48,1)}catch(t){e=this._inst.exports.getsp()>>>0,c(e+40,t),this.mem.setUint8(e+48,0)}},"syscall/js.valueLength":e=>{e>>>=0,n(e+16,Number.parseInt(i(e+8).length))},"syscall/js.valuePrepareString":e=>{e>>>=0;let t=U.encode(String(i(e+8)));c(e+16,t),n(e+24,t.length)},"syscall/js.valueLoadString":e=>{e>>>=0;let t=i(e+8);o(e+16).set(t)},"syscall/js.valueInstanceOf":e=>{e>>>=0,this.mem.setUint8(e+24,i(e+8)instanceof i(e+16)?1:0)},"syscall/js.copyBytesToGo":e=>{e>>>=0;let t=o(e+8),a=i(e+32);if(!(a instanceof Uint8Array||a instanceof Uint8ClampedArray)){this.mem.setUint8(e+48,0);return}let l=a.subarray(0,t.length);t.set(l),n(e+40,l.length),this.mem.setUint8(e+48,1)},"syscall/js.copyBytesToJS":e=>{e>>>=0;let t=i(e+8),a=o(e+16);if(!(t instanceof Uint8Array||t instanceof Uint8ClampedArray)){this.mem.setUint8(e+48,0);return}let l=a.subarray(0,t.length);t.set(l),n(e+40,l.length),this.mem.setUint8(e+48,1)},debug:e=>{console.log(e)}}}}async run(n){if(!(n instanceof WebAssembly.Instance))throw new Error("Go.run: WebAssembly.Instance expected");this._inst=n,this.mem=new DataView(this._inst.exports.mem.buffer),this._values=[Number.NaN,0,null,!0,!1,globalThis,this],this._goRefCounts=new Array(this._values.length).fill(Number.POSITIVE_INFINITY),this._ids=new Map([[0,1],[null,2],[!0,3],[!1,4],[globalThis,5],[this,6]]),this._idPool=[],this.exited=!1;let s=4096,i=u=>{let e=s,t=U.encode(`${u}\0`);return new Uint8Array(this.mem.buffer,s,t.length).set(t),s+=t.length,s%8!==0&&(s+=8-s%8),e},c=this.argv.length,o=[];this.argv.forEach(u=>{o.push(i(u))}),o.push(0),Object.keys(this.env).sort().forEach(u=>{o.push(i(`${u}=${this.env[u]}`))}),o.push(0);let f=s;o.forEach(u=>{this.mem.setUint32(s,u,!0),this.mem.setUint32(s+4,0,!0),s+=8}),this._inst.exports.run(c,f),this.exited&&this._resolveExitPromise(),await this._exitPromise}_resume(){if(this.exited)throw new Error("Go program has already exited");this._inst.exports.resume(),this.exited&&this._resolveExitPromise()}_makeFuncWrapper(n){let s=this;return function(){let i={id:n,this:this,args:arguments};return s._pendingEvent=i,s._resume(),i.result}}};var D=async(r,n)=>w().then(s=>s.transform(r,n)),V=async(r,n)=>w().then(s=>s.parse(r,n)),F=async(r,n)=>w().then(s=>s.convertToTSX(r,n)),L=async r=>{let{default:n}=await import(`data:text/javascript;charset=utf-8;base64,${Buffer.from(r).toString("base64")}`);return n},h,W=()=>{h=void 0,globalThis["@astrojs/compiler"]=void 0},w=()=>(h||(h=M().catch(r=>{throw h=void 0,r})),h),X=async(r,n)=>{let s;return s=await(async()=>{let c=await S.promises.readFile(r).then(o=>o.buffer);return WebAssembly.instantiate(new Uint8Array(c),n)})(),s},M=async()=>{let r=new y,n=await X((0,j.fileURLToPath)(new URL("../astro.wasm",b)),r.importObject);r.run(n.instance);let s=globalThis["@astrojs/compiler"];return{transform:(i,c)=>new Promise(o=>{try{o(s.transform(i,c||{}))}catch(d){throw h=void 0,d}}),parse:(i,c)=>new Promise(o=>o(s.parse(i,c||{}))).catch(o=>{throw h=void 0,o}).then(o=>({...o,ast:JSON.parse(o.ast)})),convertToTSX:(i,c)=>new Promise(o=>o(s.convertToTSX(i,c||{}))).catch(o=>{throw h=void 0,o}).then(o=>({...o,map:JSON.parse(o.map)}))}};0&&(module.exports={compile,convertToTSX,parse,teardown,transform});
