@import"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Satoshi:wght@300;400;500;600;700;800;900&display=swap";

*,
:before,
:after {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style:
}

::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style:
}

*,
:before,
:after {
    box-sizing: border-box;
    border-width: 0;
    border-style: solid;
    border-color: #e5e7eb
}

:before,
:after {
    --tw-content: ""
}

html,
:host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4;
    font-family: Inter, system-ui, sans-serif;
    font-feature-settings: normal;
    font-variation-settings: normal;
    -webkit-tap-highlight-color: transparent
}

body {
    margin: 0;
    line-height: inherit
}

hr {
    height: 0;
    color: inherit;
    border-top-width: 1px
}

abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: inherit;
    font-weight: inherit
}

a {
    color: inherit;
    text-decoration: inherit
}

b,
strong {
    font-weight: bolder
}

code,
kbd,
samp,
pre {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
    font-feature-settings: normal;
    font-variation-settings: normal;
    font-size: 1em
}

small {
    font-size: 80%
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse
}

button,
input,
optgroup,
select,
textarea {
    font-family: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    font-size: 100%;
    font-weight: inherit;
    line-height: inherit;
    letter-spacing: inherit;
    color: inherit;
    margin: 0;
    padding: 0
}

button,
select {
    text-transform: none
}

button,
input:where([type=button]),
input:where([type=reset]),
input:where([type=submit]) {
    -webkit-appearance: button;
    background-color: transparent;
    background-image: none
}

:-moz-focusring {
    outline: auto
}

:-moz-ui-invalid {
    box-shadow: none
}

progress {
    vertical-align: baseline
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

summary {
    display: list-item
}

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
    margin: 0
}

fieldset {
    margin: 0;
    padding: 0
}

legend {
    padding: 0
}

ol,
ul,
menu {
    list-style: none;
    margin: 0;
    padding: 0
}

dialog {
    padding: 0
}

textarea {
    resize: vertical
}

input::-moz-placeholder,
textarea::-moz-placeholder {
    opacity: 1;
    color: #9ca3af
}

input::placeholder,
textarea::placeholder {
    opacity: 1;
    color: #9ca3af
}

button,
[role=button] {
    cursor: pointer
}

:disabled {
    cursor: default
}

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
    display: block;
    vertical-align: middle
}

img,
video {
    max-width: 100%;
    height: auto
}

[hidden]:where(:not([hidden=until-found])) {
    display: none
}

html {
    scroll-behavior: smooth
}

body {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
    font-family: Inter, system-ui, sans-serif;
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
    background-image: radial-gradient(circle at 25% 25%, rgba(30, 64, 175, .1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(20, 184, 166, .1) 0%, transparent 50%);
    background-attachment: fixed
}

.glass-card {
    border-radius: 0rem;
    border-width: 1px;
    border-color: #ffffff1a;
    background-color: #ffffff0d;
    --tw-backdrop-blur: blur(4px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    background-image: linear-gradient(135deg, #ffffff1a, #ffffff0d)
}

.gradient-text {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
    --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
    --tw-gradient-to: #2dd4bf var(--tw-gradient-to-position);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    position: relative
}

.title-hover-glow {
    transition: all .3s ease;
    cursor: pointer
}

.title-hover-glow:hover {
    transform: translateY(-2px);
    filter: drop-shadow(0 5px 15px rgba(59, 130, 246, .4));
    text-shadow: 0 0 20px rgba(59, 130, 246, .6)
}

.neon-glow {
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    box-shadow: 0 0 20px #3b82f64d, 0 0 40px #3b82f61a
}

@keyframes neonPulse {
    0% {
        box-shadow: 0 0 5px #3b82f680, 0 0 25px #3b82f64d, 0 0 50px #3b82f61a
    }

    to {
        box-shadow: 0 0 10px #3b82f6cc, 0 0 35px #3b82f680, 0 0 70px #3b82f64d
    }
}

.hover-lift {
    transition-property: all;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .3s
}

.hover-lift:hover {
    --tw-translate-y: -.25rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / .25);
    --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.section-padding {
    padding: 4rem 1rem
}

@media (min-width: 640px) {
    .section-padding {
        padding-left: 1.5rem;
        padding-right: 1.5rem
    }
}

@media (min-width: 1024px) {
    .section-padding {
        padding-left: 2rem;
        padding-right: 2rem
    }
}

.container-width {
    margin-left: auto;
    margin-right: auto;
    max-width: 80rem
}

.team-card-enhanced {
    transform-style: preserve-3d;
    transition: all .4s cubic-bezier(.175, .885, .32, 1.275)
}

.team-card-enhanced:hover {
    transform: translateY(-10px) rotateX(5deg);
    box-shadow: 0 20px 40px #0000004d, 0 0 30px #3b82f633
}

.team-card-enhanced:before {
    content: "";
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, #3b82f60d, #14b8a60d);
    opacity: 0;
    transition: opacity .3s ease;
    z-index: -1
}

.team-card-enhanced:hover:before {
    opacity: 1
}

.pointer-events-none {
    pointer-events: none
}

.fixed {
    position: fixed
}

.absolute {
    position: absolute
}

.relative {
    position: relative
}

.inset-0 {
    inset: 0
}

.bottom-1\/4 {
    bottom: 25%
}

.bottom-4 {
    bottom: 1rem
}

.bottom-6 {
    bottom: 1.5rem
}

.bottom-8 {
    bottom: 2rem
}

.left-0 {
    left: 0
}

.left-1\/2 {
    left: 50%
}

.left-1\/4 {
    left: 25%
}

.left-4 {
    left: 1rem
}

.left-6 {
    left: 1.5rem
}

.right-0 {
    right: 0
}

.right-1\/4 {
    right: 25%
}

.right-4 {
    right: 1rem
}

.top-0 {
    top: 0
}

.top-1\/2 {
    top: 50%
}

.top-1\/4 {
    top: 25%
}

.z-0 {
    z-index: 0
}

.z-10 {
    z-index: 10
}

.z-50 {
    z-index: 50
}

.mx-auto {
    margin-left: auto;
    margin-right: auto
}

.mb-1 {
    margin-bottom: .25rem
}

.mb-12 {
    margin-bottom: 3rem
}

.mb-16 {
    margin-bottom: 4rem
}

.mb-2 {
    margin-bottom: .5rem
}

.mb-3 {
    margin-bottom: .75rem
}

.mb-4 {
    margin-bottom: 1rem
}

.mb-6 {
    margin-bottom: 1.5rem
}

.mb-8 {
    margin-bottom: 2rem
}

.ml-2 {
    margin-left: .5rem
}

.mr-2 {
    margin-right: .5rem
}

.mr-3 {
    margin-right: .75rem
}

.mr-4 {
    margin-right: 1rem
}

.mt-1 {
    margin-top: .25rem
}

.mt-12 {
    margin-top: 3rem
}

.mt-8 {
    margin-top: 2rem
}

.line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2
}

.block {
    display: block
}

.inline-block {
    display: inline-block
}

.flex {
    display: flex
}

.inline-flex {
    display: inline-flex
}

.grid {
    display: grid
}

.hidden {
    display: none
}

.h-10 {
    height: 2.5rem
}

.h-12 {
    height: 3rem
}

.h-16 {
    height: 4rem
}

.h-24 {
    height: 6rem
}

.h-3 {
    height: .75rem
}

.h-4 {
    height: 1rem
}

.h-48 {
    height: 12rem
}

.h-5 {
    height: 1.25rem
}

.h-6 {
    height: 1.5rem
}

.h-64 {
    height: 16rem
}

.h-72 {
    height: 18rem
}

.h-8 {
    height: 2rem
}

.h-96 {
    height: 24rem
}

.h-full {
    height: 100%
}

.min-h-\[50vh\] {
    min-height: 50vh
}

.min-h-\[60vh\] {
    min-height: 60vh
}

.min-h-screen {
    min-height: 100vh
}

.w-10 {
    width: 2.5rem
}

.w-12 {
    width: 3rem
}

.w-16 {
    width: 4rem
}

.w-24 {
    width: 6rem
}

.w-3 {
    width: .75rem
}

.w-4 {
    width: 1rem
}

.w-48 {
    width: 12rem
}

.w-5 {
    width: 1.25rem
}

.w-6 {
    width: 1.5rem
}

.w-72 {
    width: 18rem
}

.w-8 {
    width: 2rem
}

.w-96 {
    width: 24rem
}

.w-auto {
    width: auto
}

.w-full {
    width: 100%
}

.max-w-2xl {
    max-width: 42rem
}

.max-w-3xl {
    max-width: 48rem
}

.max-w-4xl {
    max-width: 56rem
}

.max-w-md {
    max-width: 28rem
}

.flex-1 {
    flex: 1 1 0%
}

.flex-shrink-0 {
    flex-shrink: 0
}

.grow {
    flex-grow: 1
}

.-translate-x-1\/2 {
    --tw-translate-x: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

@keyframes bounce {

    0%,
    to {
        transform: translateY(-25%);
        animation-timing-function: cubic-bezier(.8, 0, 1, 1)
    }

    50% {
        transform: none;
        animation-timing-function: cubic-bezier(0, 0, .2, 1)
    }
}

.animate-bounce {
    animation: bounce 1s infinite
}

@keyframes fadeIn {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

.animate-fade-in {
    animation: fadeIn .5s ease-in-out
}

@keyframes float {

    0%,
    to {
        transform: translateY(0)
    }

    50% {
        transform: translateY(-10px)
    }
}

.animate-float {
    animation: float 3s ease-in-out infinite
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr))
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr))
}

.flex-col {
    flex-direction: column
}

.items-start {
    align-items: flex-start
}

.items-center {
    align-items: center
}

.justify-center {
    justify-content: center
}

.justify-between {
    justify-content: space-between
}

.gap-12 {
    gap: 3rem
}

.gap-4 {
    gap: 1rem
}

.gap-8 {
    gap: 2rem
}

.space-x-3>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(.75rem * var(--tw-space-x-reverse));
    margin-left: calc(.75rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-4>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-6>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-8>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-y-1>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(.25rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(.25rem * var(--tw-space-y-reverse))
}

.space-y-12>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(3rem * var(--tw-space-y-reverse))
}

.space-y-2>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(.5rem * var(--tw-space-y-reverse))
}

.space-y-4>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse))
}

.space-y-6>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse))
}

.space-y-8>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(2rem * var(--tw-space-y-reverse))
}

.overflow-hidden {
    overflow: hidden
}

.rounded-2xl {
    border-radius: 0rem
}

.rounded-full {
    border-radius: 9999px
}

.rounded-lg {
    border-radius: .5rem
}

.border {
    border-width: 1px
}

.border-2 {
    border-width: 2px
}

.border-b {
    border-bottom-width: 1px
}

.border-t {
    border-top-width: 1px
}

.border-accent-600\/30 {
    border-color: #d977064d
}

.border-primary-600\/30 {
    border-color: #1e40af4d
}

.border-secondary-600\/30 {
    border-color: #0d94884d
}

.border-white\/10 {
    border-color: #ffffff1a
}

.border-white\/20 {
    border-color: #fff3
}

.bg-accent-500\/10 {
    background-color: #f59e0b1a
}

.bg-accent-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(217 119 6 / var(--tw-bg-opacity, 1))
}

.bg-accent-600\/20 {
    background-color: #d9770633
}

.bg-black\/40 {
    background-color: #0006
}

.bg-dark-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(9 9 11 / var(--tw-bg-opacity, 1))
}

.bg-dark-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1))
}

.bg-green-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1))
}

.bg-green-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1))
}

.bg-green-600\/20 {
    background-color: #16a34a33
}

.bg-primary-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1))
}

.bg-primary-600\/10 {
    background-color: #1e40af1a
}

.bg-primary-600\/20 {
    background-color: #1e40af33
}

.bg-red-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1))
}

.bg-secondary-500\/10 {
    background-color: #14b8a61a
}

.bg-secondary-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(13 148 136 / var(--tw-bg-opacity, 1))
}

.bg-secondary-600\/20 {
    background-color: #0d948833
}

.bg-white\/10 {
    background-color: #ffffff1a
}

.bg-white\/5 {
    background-color: #ffffff0d
}

.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops))
}

.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops))
}

.bg-gradient-to-t {
    background-image: linear-gradient(to top, var(--tw-gradient-stops))
}

.from-dark-200 {
    --tw-gradient-from: #000000 var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-primary-600\/10 {
    --tw-gradient-from: rgb(30 64 175 / .1) var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(30 64 175 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-primary-900 {
    --tw-gradient-from: #1e293b var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.via-dark-100 {
    --tw-gradient-to: rgb(9 9 11 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), #09090b var(--tw-gradient-via-position), var(--tw-gradient-to)
}

.via-transparent {
    --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to)
}

.to-secondary-900 {
    --tw-gradient-to: #134e4a var(--tw-gradient-to-position)
}

.to-transparent {
    --tw-gradient-to: transparent var(--tw-gradient-to-position)
}

.object-contain {
    -o-object-fit: contain;
    object-fit: contain
}

.object-cover {
    -o-object-fit: cover;
    object-fit: cover
}

.p-0 {
    padding: 0
}

.p-1 {
    padding: .25rem
}

.p-2 {
    padding: .5rem
}

.p-3 {
    padding: .75rem
}

.p-4 {
    padding: 1rem
}

.p-6 {
    padding: 1.5rem
}

.p-8 {
    padding: 2rem
}

.px-3 {
    padding-left: .75rem;
    padding-right: .75rem
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem
}

.py-1 {
    padding-top: .25rem;
    padding-bottom: .25rem
}

.py-2 {
    padding-top: .5rem;
    padding-bottom: .5rem
}

.py-3 {
    padding-top: .75rem;
    padding-bottom: .75rem
}

.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem
}

.pb-4 {
    padding-bottom: 1rem
}

.pt-4 {
    padding-top: 1rem
}

.pt-8 {
    padding-top: 2rem
}

.text-center {
    text-align: center
}

.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem
}

.text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem
}

.text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem
}

.text-5xl {
    font-size: 3rem;
    line-height: 1
}

.text-6xl {
    font-size: 3.75rem;
    line-height: 1
}

.text-base {
    font-size: 1rem;
    line-height: 1.5rem
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem
}

.text-sm {
    font-size: .875rem;
    line-height: 1.25rem
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem
}

.font-bold {
    font-weight: 700
}

.font-light {
    font-weight: 300
}

.font-medium {
    font-weight: 500
}

.font-normal {
    font-weight: 400
}

.font-semibold {
    font-weight: 600
}

.italic {
    font-style: italic
}

.leading-relaxed {
    line-height: 1.625
}

.text-accent-400 {
    --tw-text-opacity: 1;
    color: rgb(251 191 36 / var(--tw-text-opacity, 1))
}

.text-blue-400 {
    --tw-text-opacity: 1;
    color: rgb(96 165 250 / var(--tw-text-opacity, 1))
}

.text-green-400 {
    --tw-text-opacity: 1;
    color: rgb(74 222 128 / var(--tw-text-opacity, 1))
}

.text-primary-400 {
    --tw-text-opacity: 1;
    color: rgb(96 165 250 / var(--tw-text-opacity, 1))
}

.text-secondary-400 {
    --tw-text-opacity: 1;
    color: rgb(45 212 191 / var(--tw-text-opacity, 1))
}

.text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1))
}

.text-white\/60 {
    color: #fff9
}

.text-white\/70 {
    color: #ffffffb3
}

.text-white\/80 {
    color: #fffc
}

.text-white\/90 {
    color: #ffffffe6
}

.placeholder-white\/50::-moz-placeholder {
    color: #ffffff80
}

.placeholder-white\/50::placeholder {
    color: #ffffff80
}

.opacity-0 {
    opacity: 0
}

.opacity-20 {
    opacity: .2
}

.opacity-60 {
    opacity: .6
}

.opacity-70 {
    opacity: .7
}

.shadow-2xl {
    --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / .25);
    --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.blur-3xl {
    --tw-blur: blur(64px);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.sepia {
    --tw-sepia: sepia(100%);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.filter {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-opacity {
    transition-property: opacity;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.duration-300 {
    transition-duration: .3s
}

.duration-500 {
    transition-duration: .5s
}

.ease-out {
    transition-timing-function: cubic-bezier(0, 0, .2, 1)
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, .3)
}

.animate-fade-up {
    animation: fadeUp .6s ease-out forwards
}

.pulse-animation {
    animation: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite
}

@keyframes fadeUp {
    0% {
        opacity: 0;
        transform: translateY(30px)
    }

    to {
        opacity: 1;
        transform: translateY(0)
    }
}

@keyframes pulse {

    0%,
    to {
        opacity: 1
    }

    50% {
        opacity: .8
    }
}

::-webkit-scrollbar {
    width: 8px
}

::-webkit-scrollbar-track {
    --tw-bg-opacity: 1;
    background-color: rgb(9 9 11 / var(--tw-bg-opacity, 1))
}

::-webkit-scrollbar-thumb {
    border-radius: 9999px;
    --tw-bg-opacity: 1;
    background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1))
}

::-webkit-scrollbar-thumb:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1))
}

.hover\:scale-105:hover {
    --tw-scale-x: 1.05;
    --tw-scale-y: 1.05;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.hover\:scale-110:hover {
    --tw-scale-x: 1.1;
    --tw-scale-y: 1.1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.hover\:bg-accent-600\/30:hover {
    background-color: #d977064d
}

.hover\:bg-blue-500\/20:hover {
    background-color: #3b82f633
}

.hover\:bg-green-500\/20:hover {
    background-color: #22c55e33
}

.hover\:bg-green-600:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1))
}

.hover\:bg-green-600\/30:hover {
    background-color: #16a34a4d
}

.hover\:bg-green-700:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1))
}

.hover\:bg-primary-500\/20:hover {
    background-color: #3b82f633
}

.hover\:bg-primary-600\/30:hover {
    background-color: #1e40af4d
}

.hover\:bg-primary-700:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1))
}

.hover\:bg-red-700:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1))
}

.hover\:bg-secondary-500\/20:hover {
    background-color: #14b8a633
}

.hover\:bg-secondary-600\/30:hover {
    background-color: #0d94884d
}

.hover\:bg-secondary-700:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(15 118 110 / var(--tw-bg-opacity, 1))
}

.hover\:bg-white\/10:hover {
    background-color: #ffffff1a
}

.hover\:text-primary-300:hover {
    --tw-text-opacity: 1;
    color: rgb(147 197 253 / var(--tw-text-opacity, 1))
}

.hover\:text-primary-400:hover {
    --tw-text-opacity: 1;
    color: rgb(96 165 250 / var(--tw-text-opacity, 1))
}

.hover\:text-secondary-400:hover {
    --tw-text-opacity: 1;
    color: rgb(45 212 191 / var(--tw-text-opacity, 1))
}

.hover\:text-white:hover {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1))
}

.hover\:opacity-100:hover {
    opacity: 1
}

.hover\:shadow-2xl:hover {
    --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / .25);
    --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.focus\:border-transparent:focus {
    border-color: transparent
}

.focus\:outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.focus\:ring-2:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus\:ring-primary-400:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(96 165 250 / var(--tw-ring-opacity, 1))
}

.focus\:ring-primary-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1))
}

.focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px
}

.focus\:ring-offset-dark-200:focus {
    --tw-ring-offset-color: #000000
}

.group:hover .group-hover\:translate-x-1 {
    --tw-translate-x: .25rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group\/btn:hover .group-hover\/btn\:scale-110,
.group:hover .group-hover\:scale-110 {
    --tw-scale-x: 1.1;
    --tw-scale-y: 1.1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group:hover .group-hover\:opacity-100 {
    opacity: 1
}

@media (min-width: 640px) {
    .sm\:block {
        display: block
    }

    .sm\:flex-row {
        flex-direction: row
    }

    .sm\:px-6 {
        padding-left: 1.5rem;
        padding-right: 1.5rem
    }

    .sm\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem
    }

    .sm\:text-5xl {
        font-size: 3rem;
        line-height: 1
    }
}

@media (min-width: 768px) {
    .md\:col-span-2 {
        grid-column: span 2 / span 2
    }

    .md\:flex {
        display: flex
    }

    .md\:hidden {
        display: none
    }

    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr))
    }

    .md\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr))
    }

    .md\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr))
    }

    .md\:flex-row {
        flex-direction: row
    }

    .md\:space-y-0>:not([hidden])~:not([hidden]) {
        --tw-space-y-reverse: 0;
        margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
        margin-bottom: calc(0px * var(--tw-space-y-reverse))
    }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr))
    }

    .lg\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr))
    }

    .lg\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr))
    }

    .lg\:p-12 {
        padding: 3rem
    }

    .lg\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem
    }

    .lg\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem
    }

    .lg\:text-3xl {
        font-size: 1.875rem;
        line-height: 2.25rem
    }

    .lg\:text-4xl {
        font-size: 2.25rem;
        line-height: 2.5rem
    }

    .lg\:text-5xl {
        font-size: 3rem;
        line-height: 1
    }

    .lg\:text-7xl {
        font-size: 4.5rem;
        line-height: 1
    }
}

@media (min-width: 1280px) {
    .xl\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr))
    }
}